import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Image,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { useAuth } from '../../context/AuthContext';
import { useToast } from '../../context/ToastContext';
import { useCustomerSalon } from '../../context/CustomerSalonContext';
import {
  DarkTitle,
  DarkBody,
} from '../../components/common/Typography';
import { ScreenWrapper } from '../../components/common/ScreenWrapper';
import { Colors } from '../../constants/colors';
import { ServiceResponse } from '../../types/service';
import { customerServiceService } from '../../services/customerServiceService';
import { bookingService } from '../../services/bookingService';
import { AvailabilityResponse, AvailableStaff, TimeSlot } from '../../types/booking';

const { width, height } = Dimensions.get('window');

type ServiceBookingScreenRouteProp = RouteProp<{
  ServiceBooking: {
    serviceId: string;
  };
}, 'ServiceBooking'>;

export const ServiceBookingScreen: React.FC = () => {
  const { user } = useAuth();
  const { showToast } = useToast();
  const { selectedSalon: customerSalon } = useCustomerSalon();
  const navigation = useNavigation();
  const route = useRoute<ServiceBookingScreenRouteProp>();
  const { serviceId } = route.params;

  // State
  const [loading, setLoading] = useState(true);
  const [service, setService] = useState<ServiceResponse | null>(null);
  const [selectedDate, setSelectedDate] = useState<string>('');
  const [availability, setAvailability] = useState<AvailabilityResponse | null>(null);
  const [selectedStaff, setSelectedStaff] = useState<AvailableStaff | null>(null);
  const [selectedTimeSlot, setSelectedTimeSlot] = useState<TimeSlot | null>(null);
  const [bookingStep, setBookingStep] = useState<'date' | 'staff' | 'time' | 'confirm'>('date');
  const [isBooking, setIsBooking] = useState(false);

  // Initialize with today's date
  useEffect(() => {
    const today = new Date();
    const todayString = today.toISOString().split('T')[0];
    setSelectedDate(todayString);
  }, []);

  // Load service details
  useEffect(() => {
    loadServiceDetails();
  }, [serviceId]);

  // Load availability when date changes
  useEffect(() => {
    if (selectedDate && service && customerSalon?.salon?.id) {
      loadAvailability();
    }
  }, [selectedDate, service, customerSalon]);

  const loadServiceDetails = async () => {
    try {
      setLoading(true);
      
      if (!customerSalon?.salon?.id) {
        showToast('No salon selected', 'error');
        return;
      }

      const serviceDetails = await customerServiceService.getServiceById(serviceId);
      setService(serviceDetails);
      
      console.log('✅ ServiceBookingScreen: Loaded service:', serviceDetails.name);
    } catch (error: any) {
      console.error('❌ ServiceBookingScreen: Error loading service:', error);
      showToast('Failed to load service details', 'error');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  const loadAvailability = async () => {
    try {
      if (!customerSalon?.salon?.id || !service || !selectedDate) return;

      console.log('🔍 ServiceBookingScreen: Loading availability for:', selectedDate);

      const availabilityData = await bookingService.getAvailability({
        salonId: customerSalon.salon.id,
        serviceId: service.id,
        date: selectedDate,
      });

      setAvailability(availabilityData);
      setSelectedStaff(null);
      setSelectedTimeSlot(null);
      
      console.log('✅ ServiceBookingScreen: Loaded availability:', availabilityData.availableStaff.length, 'staff available');
    } catch (error: any) {
      console.error('❌ ServiceBookingScreen: Error loading availability:', error);
      showToast('Failed to load availability', 'error');
    }
  };

  const generateDateOptions = () => {
    const dates = [];
    const today = new Date();
    
    for (let i = 0; i < 14; i++) { // Next 14 days
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push({
        date: date.toISOString().split('T')[0],
        display: date.toLocaleDateString('en-US', { 
          weekday: 'short', 
          month: 'short', 
          day: 'numeric' 
        }),
        isToday: i === 0,
      });
    }
    
    return dates;
  };

  const handleDateSelect = (date: string) => {
    setSelectedDate(date);
    setBookingStep('staff');
  };

  const handleStaffSelect = (staff: AvailableStaff) => {
    setSelectedStaff(staff);
    setBookingStep('time');
  };

  const handleTimeSlotSelect = (timeSlot: TimeSlot) => {
    setSelectedTimeSlot(timeSlot);
    setBookingStep('confirm');
  };

  const handleConfirmBooking = async () => {
    try {
      if (!user || !customerSalon?.salon?.id || !service || !selectedStaff || !selectedTimeSlot) {
        showToast('Missing booking information', 'error');
        return;
      }

      setIsBooking(true);

      const bookingData = {
        salonId: customerSalon.salon.id,
        customerId: user.id,
        serviceId: service.id,
        staffSalonAccessId: selectedStaff.staffSalonAccessId,
        bookingDate: selectedDate,
        startTime: selectedTimeSlot.startTime,
        endTime: selectedTimeSlot.endTime,
        notes: `Booking for ${service.name}`,
      };

      const booking = await bookingService.createBooking(bookingData);
      
      showToast('Booking confirmed successfully!', 'success');
      
      // Navigate to booking confirmation or bookings list
      (navigation as any).navigate('CustomerBookings');
      
    } catch (error: any) {
      console.error('❌ ServiceBookingScreen: Error creating booking:', error);
      showToast('Failed to create booking', 'error');
    } finally {
      setIsBooking(false);
    }
  };

  const renderDateSelection = () => {
    const dates = generateDateOptions();
    
    return (
      <View style={styles.stepContainer}>
        <DarkTitle level={3} style={styles.stepTitle}>
          Select Date
        </DarkTitle>
        <ScrollView horizontal showsHorizontalScrollIndicator={false} style={styles.dateScroll}>
          {dates.map((dateOption) => (
            <TouchableOpacity
              key={dateOption.date}
              style={[
                styles.dateCard,
                selectedDate === dateOption.date && styles.selectedDateCard,
                dateOption.isToday && styles.todayCard,
              ]}
              onPress={() => handleDateSelect(dateOption.date)}
            >
              <DarkBody 
                size="small" 
                style={[
                  styles.dateText,
                  selectedDate === dateOption.date && styles.selectedDateText,
                  dateOption.isToday && styles.todayText,
                ]}
              >
                {dateOption.display}
              </DarkBody>
              {dateOption.isToday && (
                <DarkBody size="small" style={styles.todayLabel}>
                  Today
                </DarkBody>
              )}
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderStaffSelection = () => {
    if (!availability || availability.availableStaff.length === 0) {
      return (
        <View style={styles.stepContainer}>
          <DarkTitle level={3} style={styles.stepTitle}>
            Select Staff
          </DarkTitle>
          <View style={styles.emptyContainer}>
            <Icon name="person-off" size={48} color={Colors.gray400} />
            <DarkBody size="medium" style={styles.emptyText}>
              No staff available for this date
            </DarkBody>
            <TouchableOpacity 
              style={styles.backButton}
              onPress={() => setBookingStep('date')}
            >
              <DarkBody size="medium" style={styles.backButtonText}>
                Choose Different Date
              </DarkBody>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.stepContainer}>
        <DarkTitle level={3} style={styles.stepTitle}>
          Select Staff
        </DarkTitle>
        <ScrollView style={styles.staffList}>
          {availability.availableStaff.map((staff) => (
            <TouchableOpacity
              key={staff.staffSalonAccessId}
              style={[
                styles.staffCard,
                selectedStaff?.staffSalonAccessId === staff.staffSalonAccessId && styles.selectedStaffCard,
              ]}
              onPress={() => handleStaffSelect(staff)}
            >
              <View style={styles.staffInfo}>
                {staff.profileImage ? (
                  <Image source={{ uri: staff.profileImage }} style={styles.staffAvatar} />
                ) : (
                  <View style={styles.staffAvatarPlaceholder}>
                    <DarkBody size="large" style={styles.staffAvatarText}>
                      {staff.firstName.charAt(0)}{staff.lastName.charAt(0)}
                    </DarkBody>
                  </View>
                )}
                <View style={styles.staffDetails}>
                  <DarkTitle level={4} style={styles.staffName}>
                    {staff.firstName} {staff.lastName}
                  </DarkTitle>
                  <DarkBody size="small" style={styles.staffPosition}>
                    {staff.position}
                  </DarkBody>
                  <DarkBody size="small" style={styles.availableSlots}>
                    {staff.availableSlots.length} time slots available
                  </DarkBody>
                </View>
              </View>
              <Icon 
                name="chevron-right" 
                size={24} 
                color={selectedStaff?.staffSalonAccessId === staff.staffSalonAccessId ? Colors.white : Colors.gray400} 
              />
            </TouchableOpacity>
          ))}
        </ScrollView>
      </View>
    );
  };

  const renderTimeSelection = () => {
    if (!selectedStaff || selectedStaff.availableSlots.length === 0) {
      return (
        <View style={styles.stepContainer}>
          <DarkTitle level={3} style={styles.stepTitle}>
            Select Time
          </DarkTitle>
          <View style={styles.emptyContainer}>
            <Icon name="schedule" size={48} color={Colors.gray400} />
            <DarkBody size="medium" style={styles.emptyText}>
              No time slots available for this staff member
            </DarkBody>
            <TouchableOpacity
              style={styles.backButton}
              onPress={() => setBookingStep('staff')}
            >
              <DarkBody size="medium" style={styles.backButtonText}>
                Choose Different Staff
              </DarkBody>
            </TouchableOpacity>
          </View>
        </View>
      );
    }

    return (
      <View style={styles.stepContainer}>
        <DarkTitle level={3} style={styles.stepTitle}>
          Select Time
        </DarkTitle>
        <DarkBody size="small" style={styles.staffSelectedText}>
          with {selectedStaff.firstName} {selectedStaff.lastName}
        </DarkBody>
        <View style={styles.timeSlotsGrid}>
          {selectedStaff.availableSlots.map((timeSlot, index) => (
            <TouchableOpacity
              key={`${timeSlot.startTime}-${index}`}
              style={[
                styles.timeSlotCard,
                selectedTimeSlot?.startTime === timeSlot.startTime && styles.selectedTimeSlotCard,
              ]}
              onPress={() => handleTimeSlotSelect(timeSlot)}
            >
              <DarkBody
                size="medium"
                style={[
                  styles.timeSlotText,
                  selectedTimeSlot?.startTime === timeSlot.startTime && styles.selectedTimeSlotText,
                ]}
              >
                {timeSlot.startTime} - {timeSlot.endTime}
              </DarkBody>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  const renderConfirmation = () => {
    if (!service || !selectedStaff || !selectedTimeSlot) return null;

    const selectedDateObj = new Date(selectedDate);
    const formattedDate = selectedDateObj.toLocaleDateString('en-US', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric',
    });

    return (
      <View style={styles.stepContainer}>
        <DarkTitle level={3} style={styles.stepTitle}>
          Confirm Booking
        </DarkTitle>

        <View style={styles.confirmationCard}>
          <View style={styles.confirmationSection}>
            <DarkTitle level={4} style={styles.confirmationSectionTitle}>
              Service
            </DarkTitle>
            <DarkBody size="medium" style={styles.confirmationText}>
              {service.name}
            </DarkBody>
            <DarkBody size="small" style={styles.confirmationSubtext}>
              {service.duration} minutes • ${service.price}
            </DarkBody>
          </View>

          <View style={styles.confirmationSection}>
            <DarkTitle level={4} style={styles.confirmationSectionTitle}>
              Staff
            </DarkTitle>
            <DarkBody size="medium" style={styles.confirmationText}>
              {selectedStaff.firstName} {selectedStaff.lastName}
            </DarkBody>
            <DarkBody size="small" style={styles.confirmationSubtext}>
              {selectedStaff.position}
            </DarkBody>
          </View>

          <View style={styles.confirmationSection}>
            <DarkTitle level={4} style={styles.confirmationSectionTitle}>
              Date & Time
            </DarkTitle>
            <DarkBody size="medium" style={styles.confirmationText}>
              {formattedDate}
            </DarkBody>
            <DarkBody size="small" style={styles.confirmationSubtext}>
              {selectedTimeSlot.startTime} - {selectedTimeSlot.endTime}
            </DarkBody>
          </View>

          <View style={styles.confirmationSection}>
            <DarkTitle level={4} style={styles.confirmationSectionTitle}>
              Location
            </DarkTitle>
            <DarkBody size="medium" style={styles.confirmationText}>
              {customerSalon?.salon?.name}
            </DarkBody>
            <DarkBody size="small" style={styles.confirmationSubtext}>
              {customerSalon?.salon?.address}
            </DarkBody>
          </View>
        </View>

        <View style={styles.totalSection}>
          <View style={styles.totalRow}>
            <DarkTitle level={4} style={styles.totalLabel}>
              Total Amount
            </DarkTitle>
            <DarkTitle level={3} style={styles.totalAmount}>
              ${service.price}
            </DarkTitle>
          </View>
        </View>
      </View>
    );
  };

  if (loading) {
    return (
      <ScreenWrapper>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <DarkBody size="medium" style={styles.loadingText}>
            Loading service details...
          </DarkBody>
        </View>
      </ScreenWrapper>
    );
  }

  if (!service) {
    return (
      <ScreenWrapper>
        <View style={styles.errorContainer}>
          <Icon name="error" size={48} color={Colors.error} />
          <DarkTitle level={3} style={styles.errorTitle}>
            Service Not Found
          </DarkTitle>
          <DarkBody size="medium" style={styles.errorText}>
            The requested service could not be found.
          </DarkBody>
          <TouchableOpacity 
            style={styles.backButton}
            onPress={() => navigation.goBack()}
          >
            <DarkBody size="medium" style={styles.backButtonText}>
              Go Back
            </DarkBody>
          </TouchableOpacity>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity 
          style={styles.backIcon}
          onPress={() => navigation.goBack()}
        >
          <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <DarkTitle level={3} style={styles.headerTitle}>
          Book Service
        </DarkTitle>
      </View>

      {/* Service Info */}
      <View style={styles.serviceInfo}>
        {service.image && (
          <Image source={{ uri: service.image }} style={styles.serviceImage} />
        )}
        <View style={styles.serviceDetails}>
          <DarkTitle level={4} style={styles.serviceName}>
            {service.name}
          </DarkTitle>
          <DarkBody size="small" style={styles.serviceCategory}>
            {service.category}
          </DarkBody>
          <View style={styles.serviceMeta}>
            <DarkBody size="small" style={styles.servicePrice}>
              ${service.price}
            </DarkBody>
            <DarkBody size="small" style={styles.serviceDuration}>
              • {service.duration} min
            </DarkBody>
          </View>
        </View>
      </View>

      {/* Booking Steps */}
      <ScrollView style={styles.content}>
        {bookingStep === 'date' && renderDateSelection()}
        {bookingStep === 'staff' && renderStaffSelection()}
        {bookingStep === 'time' && renderTimeSelection()}
        {bookingStep === 'confirm' && renderConfirmation()}
      </ScrollView>

      {/* Bottom Action Button */}
      {bookingStep === 'confirm' && (
        <View style={styles.bottomAction}>
          <TouchableOpacity
            style={[styles.confirmButton, isBooking && styles.disabledButton]}
            onPress={handleConfirmBooking}
            disabled={isBooking}
          >
            {isBooking ? (
              <ActivityIndicator size="small" color={Colors.white} />
            ) : (
              <DarkBody size="medium" style={styles.confirmButtonText}>
                Confirm Booking
              </DarkBody>
            )}
          </TouchableOpacity>
        </View>
      )}
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  backIcon: {
    marginRight: width * 0.04,
  },
  headerTitle: {
    color: Colors.textPrimary,
  },
  serviceInfo: {
    flexDirection: 'row',
    padding: width * 0.05,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  serviceImage: {
    width: width * 0.2,
    height: width * 0.2,
    borderRadius: width * 0.02,
    marginRight: width * 0.04,
  },
  serviceDetails: {
    flex: 1,
    justifyContent: 'center',
  },
  serviceName: {
    color: Colors.textPrimary,
    marginBottom: height * 0.005,
  },
  serviceCategory: {
    color: Colors.textSecondary,
    marginBottom: height * 0.01,
  },
  serviceMeta: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  servicePrice: {
    color: Colors.primary,
    fontWeight: '600',
  },
  serviceDuration: {
    color: Colors.textSecondary,
  },
  content: {
    flex: 1,
  },
  stepContainer: {
    padding: width * 0.05,
  },
  stepTitle: {
    color: Colors.textPrimary,
    marginBottom: height * 0.02,
  },
  dateScroll: {
    flexDirection: 'row',
  },
  dateCard: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.02,
    padding: width * 0.03,
    marginRight: width * 0.03,
    minWidth: width * 0.2,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.gray200,
  },
  selectedDateCard: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  todayCard: {
    borderColor: Colors.secondary,
  },
  dateText: {
    color: Colors.textPrimary,
    textAlign: 'center',
  },
  selectedDateText: {
    color: Colors.white,
  },
  todayText: {
    color: Colors.secondary,
    fontWeight: '600',
  },
  todayLabel: {
    color: Colors.secondary,
    fontSize: 10,
    marginTop: 2,
  },
  staffList: {
    flex: 1,
  },
  staffCard: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.03,
    padding: width * 0.04,
    marginBottom: height * 0.015,
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 1,
    borderColor: Colors.gray200,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  selectedStaffCard: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  staffInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  staffAvatar: {
    width: width * 0.12,
    height: width * 0.12,
    borderRadius: width * 0.06,
    marginRight: width * 0.03,
  },
  staffAvatarPlaceholder: {
    width: width * 0.12,
    height: width * 0.12,
    borderRadius: width * 0.06,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: width * 0.03,
  },
  staffAvatarText: {
    color: Colors.white,
    fontWeight: 'bold',
  },
  staffDetails: {
    flex: 1,
  },
  staffName: {
    color: Colors.textPrimary,
    marginBottom: height * 0.005,
  },
  staffPosition: {
    color: Colors.textSecondary,
    marginBottom: height * 0.005,
  },
  availableSlots: {
    color: Colors.success,
    fontWeight: '500',
  },
  staffSelectedText: {
    color: Colors.textSecondary,
    marginBottom: height * 0.02,
    fontStyle: 'italic',
  },
  timeSlotsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    justifyContent: 'space-between',
  },
  timeSlotCard: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.02,
    padding: width * 0.03,
    marginBottom: height * 0.015,
    width: (width - width * 0.15) / 2,
    alignItems: 'center',
    borderWidth: 1,
    borderColor: Colors.gray200,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
    elevation: 2,
  },
  selectedTimeSlotCard: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  timeSlotText: {
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  selectedTimeSlotText: {
    color: Colors.white,
  },
  confirmationCard: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.03,
    padding: width * 0.04,
    marginBottom: height * 0.02,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  confirmationSection: {
    marginBottom: height * 0.025,
    paddingBottom: height * 0.02,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray100,
  },
  confirmationSectionTitle: {
    color: Colors.textSecondary,
    marginBottom: height * 0.005,
    fontSize: width * 0.035,
    textTransform: 'uppercase',
    letterSpacing: 0.5,
  },
  confirmationText: {
    color: Colors.textPrimary,
    fontWeight: '600',
    marginBottom: height * 0.005,
  },
  confirmationSubtext: {
    color: Colors.textSecondary,
  },
  totalSection: {
    backgroundColor: Colors.gray50,
    borderRadius: width * 0.02,
    padding: width * 0.04,
  },
  totalRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  totalLabel: {
    color: Colors.textPrimary,
  },
  totalAmount: {
    color: Colors.primary,
    fontWeight: '700',
  },
  emptyContainer: {
    alignItems: 'center',
    paddingVertical: height * 0.05,
  },
  emptyText: {
    color: Colors.textSecondary,
    textAlign: 'center',
    marginTop: height * 0.02,
    marginBottom: height * 0.03,
  },
  backButton: {
    backgroundColor: Colors.gray200,
    paddingHorizontal: width * 0.06,
    paddingVertical: height * 0.015,
    borderRadius: width * 0.02,
  },
  backButtonText: {
    color: Colors.textPrimary,
    fontWeight: '600',
  },
  bottomAction: {
    padding: width * 0.05,
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.gray200,
  },
  confirmButton: {
    backgroundColor: Colors.primary,
    paddingVertical: height * 0.02,
    borderRadius: width * 0.02,
    alignItems: 'center',
  },
  disabledButton: {
    backgroundColor: Colors.gray400,
  },
  confirmButtonText: {
    color: Colors.white,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: Colors.textSecondary,
    marginTop: height * 0.02,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  errorTitle: {
    color: Colors.textPrimary,
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
  },
  errorText: {
    color: Colors.textSecondary,
    textAlign: 'center',
    marginBottom: height * 0.03,
  },
});
