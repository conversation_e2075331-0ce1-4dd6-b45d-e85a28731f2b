import { apiService } from './api';
import { ENDPOINTS } from '../config/api';
import {
  StaffServiceAssignment,
  CreateStaffServiceAssignmentRequest,
  UpdateStaffServiceAssignmentRequest,
  BulkAssignServicesRequest,
  AssignmentSearchFilters,
} from '../types/staffServiceAssignment';

export interface AssignmentListResponse {
  data: StaffServiceAssignment[];
  total: number;
  page: number;
  totalPages: number;
}

class StaffServiceAssignmentService {
  private readonly baseUrl = '/staff-service-assignments';

  /**
   * Create a new staff-service assignment
   */
  async createAssignment(data: CreateStaffServiceAssignmentRequest): Promise<StaffServiceAssignment> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Creating assignment');

      const response = await apiService.post(this.baseUrl, data);

      console.log('✅ StaffServiceAssignmentService: Assignment created successfully');
      return response.data;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error creating assignment:', error);
      throw error;
    }
  }

  /**
   * Get assignments with filters
   */
  async getAssignments(filters?: AssignmentSearchFilters): Promise<AssignmentListResponse> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Getting assignments with filters:', filters);

      let url = this.baseUrl;
      
      // Add query parameters
      if (filters) {
        const params = new URLSearchParams();
        
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            params.append(key, value.toString());
          }
        });

        const queryString = params.toString();
        if (queryString) {
          url += `?${queryString}`;
        }
      }

      const response = await apiService.get(url);

      console.log('✅ StaffServiceAssignmentService: Retrieved assignments:', response.data?.length || 0);
      return {
        data: response.data || [],
        total: response.pagination?.total || 0,
        page: response.pagination?.page || 1,
        totalPages: response.pagination?.totalPages || 1,
      };
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting assignments:', error);
      throw error;
    }
  }

  /**
   * Get assignment by ID
   */
  async getAssignmentById(assignmentId: string): Promise<StaffServiceAssignment> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Getting assignment by ID:', assignmentId);

      const response = await apiService.get(`${this.baseUrl}/${assignmentId}`);

      console.log('✅ StaffServiceAssignmentService: Retrieved assignment');
      return response.data;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting assignment:', error);
      throw error;
    }
  }

  /**
   * Update assignment
   */
  async updateAssignment(assignmentId: string, data: UpdateStaffServiceAssignmentRequest): Promise<StaffServiceAssignment> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Updating assignment:', assignmentId);

      const response = await apiService.put(`${this.baseUrl}/${assignmentId}`, data);

      console.log('✅ StaffServiceAssignmentService: Assignment updated successfully');
      return response.data;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error updating assignment:', error);
      throw error;
    }
  }

  /**
   * Delete assignment
   */
  async deleteAssignment(assignmentId: string): Promise<void> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Deleting assignment:', assignmentId);

      await apiService.delete(`${this.baseUrl}/${assignmentId}`);

      console.log('✅ StaffServiceAssignmentService: Assignment deleted successfully');
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error deleting assignment:', error);
      throw error;
    }
  }

  /**
   * Bulk assign services to staff
   */
  async bulkAssignServices(data: BulkAssignServicesRequest): Promise<StaffServiceAssignment[]> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Bulk assigning services');

      const response = await apiService.post(`${this.baseUrl}/bulk-assign`, data);

      console.log('✅ StaffServiceAssignmentService: Bulk assignment completed');
      return response.data;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error in bulk assignment:', error);
      throw error;
    }
  }

  /**
   * Get services assigned to staff
   */
  async getStaffServices(staffSalonAccessId: string): Promise<StaffServiceAssignment[]> {
    try {
     
      const response = await apiService.get(`${this.baseUrl}/staff/${staffSalonAccessId}/services`);

      console.log('✅ StaffServiceAssignmentService: Retrieved staff services:', response.data);
      return response.data || [];
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting staff services:', error);
      throw error;
    }
  }

  /**
   * Get staff assigned to service
   */
  async getServiceStaff(serviceId: string): Promise<StaffServiceAssignment[]> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Getting service staff:', serviceId);

      const response = await apiService.get(`${this.baseUrl}/service/${serviceId}/staff`);

      console.log('✅ StaffServiceAssignmentService: Retrieved service staff:', response.data?.length || 0);
      return response.data || [];
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting service staff:', error);
      throw error;
    }
  }

  /**
   * Get assignments for a salon
   */
  async getSalonAssignments(salonId: string, filters?: Omit<AssignmentSearchFilters, 'salonId'>): Promise<AssignmentListResponse> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Getting salon assignments:', salonId);

      const searchFilters: AssignmentSearchFilters = {
        ...filters,
        salonId,
      };

      return await this.getAssignments(searchFilters);
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting salon assignments:', error);
      throw error;
    }
  }

  /**
   * Check if staff is assigned to service
   */
  async isStaffAssignedToService(staffSalonAccessId: string, serviceId: string): Promise<boolean> {
    try {
      const assignments = await this.getAssignments({
        staffSalonAccessId,
        serviceId,
        limit: 1,
      });

      return assignments.data.length > 0 && assignments.data[0].isCurrentlyActive;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error checking assignment:', error);
      return false;
    }
  }

  /**
   * Get unassigned services for staff
   */
  async getUnassignedServices(staffSalonAccessId: string, salonId: string): Promise<any[]> {
    try {
     

      // Get all services for the salon
      const servicesResponse = await apiService.get(`/services/salon/${salonId}`);
      const allServices = servicesResponse.data.data || [];
 
      // Get assigned services for the staff
      const assignedServices = await this.getStaffServices(staffSalonAccessId);
console.log('🔍 StaffServiceAssignmentService: Getting unassigned assignedServices:', assignedServices);
      const assignedServiceIds = assignedServices.map(assignment => assignment.serviceId);

      // Filter out assigned services
      const unassignedServices = allServices.filter((service: any) => 
        !assignedServiceIds.includes(service.id) && service.isActive
      );

      console.log('✅ StaffServiceAssignmentService: Retrieved unassigned services:', unassignedServices.length);
      return unassignedServices;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting unassigned services:', error);
      throw error;
    }
  }

  /**
   * Get assignment statistics for a salon
   */
  async getAssignmentStats(salonId: string): Promise<{
    totalAssignments: number;
    activeAssignments: number;
    totalStaffWithServices: number;
    totalServicesWithStaff: number;
  }> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Getting assignment statistics');

      const assignments = await this.getSalonAssignments(salonId, { limit: 1000 });
      const allAssignments = assignments.data;

      const activeAssignments = allAssignments.filter(a => a.isCurrentlyActive);
      const uniqueStaff = new Set(activeAssignments.map(a => a.staffSalonAccessId));
      const uniqueServices = new Set(activeAssignments.map(a => a.serviceId));

      const stats = {
        totalAssignments: allAssignments.length,
        activeAssignments: activeAssignments.length,
        totalStaffWithServices: uniqueStaff.size,
        totalServicesWithStaff: uniqueServices.size,
      };

      console.log('✅ StaffServiceAssignmentService: Assignment statistics:', stats);
      return stats;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting assignment statistics:', error);
      throw error;
    }
  }
}

export const staffServiceAssignmentService = new StaffServiceAssignmentService();
