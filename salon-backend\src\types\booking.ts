import { BookingStatus, PaymentStatus } from '../entities/Booking';

export interface TimeSlot {
  startTime: string; // Format: "HH:mm"
  endTime: string;   // Format: "HH:mm"
  isAvailable: boolean;
  staffId?: string;
  staffName?: string;
}

export interface AvailableStaff {
  staffSalonAccessId: string;
  staffId: string;
  firstName: string;
  lastName: string;
  profileImage?: string;
  position: string;
  rating?: number;
  reviewCount?: number;
  specializationLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  availableSlots: TimeSlot[];
}

export interface CreateBookingRequest {
  salonId: string;
  customerId: string;
  serviceId: string;
  staffSalonAccessId: string;
  bookingDate: string; // Format: "YYYY-MM-DD"
  startTime: string;   // Format: "HH:mm"
  endTime: string;     // Format: "HH:mm"
  notes?: string;
  specialRequests?: string;
  offerId?: string; // If applying an offer
}

export interface UpdateBookingRequest {
  status?: BookingStatus;
  paymentStatus?: PaymentStatus;
  bookingDate?: string;
  startTime?: string;
  endTime?: string;
  notes?: string;
  specialRequests?: string;
  cancellationReason?: string;
}

export interface BookingFilters {
  salonId?: string;
  customerId?: string;
  serviceId?: string;
  staffSalonAccessId?: string;
  status?: BookingStatus;
  paymentStatus?: PaymentStatus;
  dateFrom?: string;
  dateTo?: string;
  search?: string;
  sortBy?: 'bookingDate' | 'createdAt' | 'finalPrice' | 'status';
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

export interface BookingResponse {
  id: string;
  bookingNumber: string;
  status: BookingStatus;
  paymentStatus: PaymentStatus;
  
  // Service details
  service: {
    id: string;
    name: string;
    category: string;
    duration: number;
    price: number;
    image?: string;
  };
  
  // Staff details
  staff: {
    staffSalonAccessId: string;
    firstName: string;
    lastName: string;
    profileImage?: string;
    position: string;
  };
  
  // Salon details
  salon: {
    id: string;
    name: string;
    address: string;
    phone: string;
    image?: string;
  };
  
  // Customer details
  customer: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  
  // Booking details
  bookingDate: string;
  startTime: string;
  endTime: string;
  duration: number;
  
  // Pricing
  originalPrice: number;
  discountAmount: number;
  finalPrice: number;
  
  // Additional info
  notes?: string;
  specialRequests?: string;
  cancellationReason?: string;
  
  // Applied offer
  appliedOffer?: {
    id: string;
    title: string;
    discountType: string;
    discountValue: number;
  };
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  confirmedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
}

export interface BookingListResponse {
  data: BookingResponse[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface AvailabilityRequest {
  salonId: string;
  serviceId: string;
  date: string; // Format: "YYYY-MM-DD"
  preferredStaffId?: string;
}

export interface AvailabilityResponse {
  date: string;
  availableStaff: AvailableStaff[];
  totalSlots: number;
  availableSlots: number;
  busySlots: number;
}

export interface BookingStats {
  totalBookings: number;
  pendingBookings: number;
  confirmedBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
}

export interface RescheduleBookingRequest {
  bookingDate: string;
  startTime: string;
  staffSalonAccessId?: string;
}

export interface CancelBookingRequest {
  cancellationReason: string;
}

export interface BookingValidationResult {
  isValid: boolean;
  errors: string[];
  warnings: string[];
  conflicts: {
    staffConflict: boolean;
    timeConflict: boolean;
    serviceConflict: boolean;
  };
}

// Utility interfaces
export interface BookingTimeSlot {
  date: string;
  startTime: string;
  endTime: string;
  staffSalonAccessId: string;
}

export interface StaffWorkingHours {
  dayOfWeek: string;
  startTime: string;
  endTime: string;
  isWorking: boolean;
}

export interface BookingConflictCheck {
  staffSalonAccessId: string;
  date: string;
  startTime: string;
  endTime: string;
  excludeBookingId?: string;
}
