const { Client } = require('pg');
require('dotenv').config();

async function createStaffServiceAssignmentTable() {
  const client = new Client({
    connectionString: process.env.DATABASE_URL,
  });

  try {
    await client.connect();
    console.log('✅ Connected to database');

    // Create the table
    const createTableSQL = `
      CREATE TABLE IF NOT EXISTS "staff_service_assignments" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "staffSalonAccessId" uuid NOT NULL,
        "serviceId" uuid NOT NULL,
        "status" character varying NOT NULL DEFAULT 'ACTIVE',
        "specializationLevel" character varying NOT NULL DEFAULT 'INTERMEDIATE',
        "commissionRate" numeric(5,2),
        "customRate" numeric(10,2),
        "isBookable" boolean NOT NULL DEFAULT true,
        "priority" integer,
        "notes" text,
        "startDate" TIMESTAMP NOT NULL,
        "endDate" TIMESTAMP,
        "assignedBy" uuid NOT NULL,
        "assignedAt" TIMESTAMP NOT NULL,
        "lastModifiedBy" uuid,
        "lastModifiedAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_staff_service_assignments" PRIMARY KEY ("id")
      );
    `;

    await client.query(createTableSQL);
    console.log('✅ Table created successfully');

    // Add unique constraint
    try {
      await client.query(`
        ALTER TABLE "staff_service_assignments" 
        ADD CONSTRAINT "UQ_staff_service_assignment" 
        UNIQUE ("staffSalonAccessId", "serviceId");
      `);
      console.log('✅ Unique constraint added');
    } catch (error) {
      if (error.code === '42P07' || error.message.includes('already exists')) {
        console.log('ℹ️ Unique constraint already exists');
      } else {
        throw error;
      }
    }

    // Add indexes
    const indexes = [
      'CREATE INDEX IF NOT EXISTS "IDX_staff_service_assignment_staff" ON "staff_service_assignments" ("staffSalonAccessId");',
      'CREATE INDEX IF NOT EXISTS "IDX_staff_service_assignment_service" ON "staff_service_assignments" ("serviceId");',
      'CREATE INDEX IF NOT EXISTS "IDX_staff_service_assignment_status" ON "staff_service_assignments" ("status");',
      'CREATE INDEX IF NOT EXISTS "IDX_staff_service_assignment_assigned_by" ON "staff_service_assignments" ("assignedBy");'
    ];

    for (const indexSQL of indexes) {
      await client.query(indexSQL);
    }
    console.log('✅ Indexes created successfully');

    // Add foreign key constraints
    const foreignKeys = [
      `ALTER TABLE "staff_service_assignments" 
       ADD CONSTRAINT "FK_staff_service_assignment_staff_access" 
       FOREIGN KEY ("staffSalonAccessId") 
       REFERENCES "staff_salon_access"("id") 
       ON DELETE CASCADE ON UPDATE NO ACTION;`,
      
      `ALTER TABLE "staff_service_assignments" 
       ADD CONSTRAINT "FK_staff_service_assignment_service" 
       FOREIGN KEY ("serviceId") 
       REFERENCES "services"("id") 
       ON DELETE CASCADE ON UPDATE NO ACTION;`,
      
      `ALTER TABLE "staff_service_assignments" 
       ADD CONSTRAINT "FK_staff_service_assignment_assigned_by" 
       FOREIGN KEY ("assignedBy") 
       REFERENCES "users"("id") 
       ON DELETE SET NULL ON UPDATE NO ACTION;`,
      
      `ALTER TABLE "staff_service_assignments" 
       ADD CONSTRAINT "FK_staff_service_assignment_last_modified_by" 
       FOREIGN KEY ("lastModifiedBy") 
       REFERENCES "users"("id") 
       ON DELETE SET NULL ON UPDATE NO ACTION;`
    ];

    for (const fkSQL of foreignKeys) {
      try {
        await client.query(fkSQL);
      } catch (error) {
        if (error.code === '42P07' || error.message.includes('already exists')) {
          console.log('ℹ️ Foreign key constraint already exists');
        } else {
          throw error;
        }
      }
    }
    console.log('✅ Foreign key constraints added successfully');

    // Add check constraints
    const checkConstraints = [
      `ALTER TABLE "staff_service_assignments" 
       ADD CONSTRAINT "CHK_staff_service_assignment_status" 
       CHECK ("status" IN ('ACTIVE', 'INACTIVE', 'SUSPENDED'));`,
      
      `ALTER TABLE "staff_service_assignments" 
       ADD CONSTRAINT "CHK_staff_service_assignment_specialization" 
       CHECK ("specializationLevel" IN ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', 'MASTER'));`,
      
      `ALTER TABLE "staff_service_assignments" 
       ADD CONSTRAINT "CHK_staff_service_assignment_commission_rate" 
       CHECK ("commissionRate" IS NULL OR ("commissionRate" >= 0 AND "commissionRate" <= 100));`,
      
      `ALTER TABLE "staff_service_assignments" 
       ADD CONSTRAINT "CHK_staff_service_assignment_custom_rate" 
       CHECK ("customRate" IS NULL OR "customRate" >= 0);`,
      
      `ALTER TABLE "staff_service_assignments" 
       ADD CONSTRAINT "CHK_staff_service_assignment_priority" 
       CHECK ("priority" IS NULL OR "priority" >= 0);`
    ];

    for (const checkSQL of checkConstraints) {
      try {
        await client.query(checkSQL);
      } catch (error) {
        if (error.code === '42P07' || error.message.includes('already exists')) {
          console.log('ℹ️ Check constraint already exists');
        } else {
          throw error;
        }
      }
    }
    console.log('✅ Check constraints added successfully');

    console.log('🎉 Staff service assignments table setup completed!');

  } catch (error) {
    console.error('❌ Error creating table:', error);
    throw error;
  } finally {
    await client.end();
  }
}

// Run the function
createStaffServiceAssignmentTable()
  .then(() => {
    console.log('✅ Script completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Script failed:', error);
    process.exit(1);
  });
