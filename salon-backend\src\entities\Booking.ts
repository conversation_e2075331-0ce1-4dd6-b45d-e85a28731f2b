import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  BeforeInsert,
} from 'typeorm';
import { Salon } from './Salon';
import { User } from './User';
import { Service } from './Service';
import { StaffSalonAccess } from './StaffSalonAccess';

export enum BookingStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  PARTIALLY_PAID = 'PARTIALLY_PAID',
  REFUNDED = 'REFUNDED',
  FAILED = 'FAILED',
}

@Entity('bookings')
@Index(['salonId', 'bookingDate'])
@Index(['customerId', 'bookingDate'])
@Index(['staffSalonAccessId', 'bookingDate'])
@Index(['status', 'bookingDate'])
export class Booking {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'varchar',
    length: 20,
    unique: true,
  })
  bookingNumber!: string;

  @Column({
    type: 'enum',
    enum: BookingStatus,
    default: BookingStatus.PENDING,
  })
  status!: BookingStatus;

  @Column({
    type: 'enum',
    enum: PaymentStatus,
    default: PaymentStatus.PENDING,
  })
  paymentStatus!: PaymentStatus;

  // Booking details
  @Column({
    type: 'date',
  })
  bookingDate!: Date;

  @Column({
    type: 'time',
  })
  startTime!: string;

  @Column({
    type: 'time',
  })
  endTime!: string;

  @Column({
    type: 'int',
    comment: 'Duration in minutes',
  })
  duration!: number;

  // Pricing
  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
  })
  originalPrice!: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    default: 0,
  })
  discountAmount!: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
  })
  finalPrice!: number;

  // Additional information
  @Column({
    type: 'text',
    nullable: true,
  })
  notes?: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  specialRequests?: string;

  @Column({
    type: 'text',
    nullable: true,
  })
  cancellationReason?: string;

  // Applied offer
  @Column({
    type: 'uuid',
    nullable: true,
  })
  appliedOfferId?: string;

  // Timestamps
  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  confirmedAt?: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  completedAt?: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
  })
  cancelledAt?: Date;

  // Foreign Keys
  @Column({
    type: 'uuid',
  })
  salonId!: string;

  @Column({
    type: 'uuid',
  })
  customerId!: string;

  @Column({
    type: 'uuid',
  })
  serviceId!: string;

  @Column({
    type: 'uuid',
  })
  staffSalonAccessId!: string;

  // Relationships
  @ManyToOne(() => Salon, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'salonId' })
  salon!: Salon;

  @ManyToOne(() => User, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'customerId' })
  customer!: User;

  @ManyToOne(() => Service, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'serviceId' })
  service!: Service;

  @ManyToOne(() => StaffSalonAccess, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'staffSalonAccessId' })
  staffSalonAccess!: StaffSalonAccess;

  @BeforeInsert()
  generateBookingNumber() {
    // Generate booking number: BK + timestamp + random
    const timestamp = Date.now().toString().slice(-6);
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0');
    this.bookingNumber = `BK${timestamp}${random}`;
  }
}
