
import { apiService } from './api';
import {
  StaffAvailability,
  CreateAvailabilityRequest,
  UpdateAvailabilityRequest,
  AvailabilitySearchFilters,
  BulkCreateAvailabilityRequest,
  AvailabilityType,
  AvailabilityStatus,
  TimeSlot
} from '../types/staffAvailability';

export interface PaginatedResponse<T> {
  data: T[];
  total: number;
  page: number;
  totalPages: number;
}

class StaffAvailabilityService {
  /**
   * Get staff availability records
   */
  async getAvailability(filters: AvailabilitySearchFilters = {}): Promise<PaginatedResponse<StaffAvailability>> {
    try {
      const queryParams = new URLSearchParams();

      console.log('🔍 StaffAvailabilityService: Building query with filters:', filters);

      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (value instanceof Date) {
            // Format date as YYYY-MM-DD to avoid JSON parsing issues
            const dateString = value.toISOString().split('T')[0];
            queryParams.append(key, dateString);
            console.log(`📅 Added date parameter: ${key}=${dateString}`);
          } else {
            queryParams.append(key, value.toString());
            console.log(`📝 Added parameter: ${key}=${value}`);
          }
        }
      });

      const endpoint = `/staff-availability${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      console.log('🌐 Making API call to:', endpoint);

      const result = await apiService.get<PaginatedResponse<StaffAvailability>>(endpoint);

      console.log('✅ API response received:', result.data?.data?.length || 0, 'records');
      return result.data;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error fetching availability:', error);
      console.error('❌ Error details:', {
        message: error.message,
        statusCode: error.statusCode,
        stack: error.stack
      });
      throw error;
    }
  }

  /**
   * Get availability by ID
   */
  async getAvailabilityById(availabilityId: string): Promise<StaffAvailability> {
    try {
      const result = await apiService.get<StaffAvailability>(`/staff-availability/${availabilityId}`);
      return result.data;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error fetching availability by ID:', error);
      throw error;
    }
  }

  /**
   * Create new availability record
   */
  async createAvailability(data: CreateAvailabilityRequest): Promise<StaffAvailability> {
    try {
      const result = await apiService.post<StaffAvailability>('/staff-availability', data);
      return result.data;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error creating availability:', error);
      throw error;
    }
  }

  /**
   * Update availability record
   */
  async updateAvailability(availabilityId: string, data: UpdateAvailabilityRequest): Promise<StaffAvailability> {
    try {
      const result = await apiService.put<StaffAvailability>(`/staff-availability/${availabilityId}`, data);
      return result.data;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error updating availability:', error);
      throw error;
    }
  }

  /**
   * Delete availability record
   */
  async deleteAvailability(availabilityId: string): Promise<void> {
    try {
      await apiService.delete(`/staff-availability/${availabilityId}`);
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error deleting availability:', error);
      throw error;
    }
  }

  /**
   * Create availability override for specific date
   */
  async createDateOverride(
    staffSalonAccessId: string,
    date: Date,
    timeSlots: TimeSlot[],
    notes?: string
  ): Promise<StaffAvailability> {
    try {
      const data: CreateAvailabilityRequest = {
        staffSalonAccessId,
        type: AvailabilityType.OVERRIDE,
        date,
        timeSlots,
        isAvailable: timeSlots.some(slot => slot.isAvailable),
        notes: notes || `Override for ${date.toLocaleDateString()}`,
      };

      return await this.createAvailability(data);
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error creating date override:', error);
      throw error;
    }
  }

  /**
   * Update time slots for existing availability
   */
  async updateTimeSlots(
    availabilityId: string,
    timeSlots: TimeSlot[],
    notes?: string
  ): Promise<StaffAvailability> {
    try {
      const data: UpdateAvailabilityRequest = {
        timeSlots,
        isAvailable: timeSlots.some(slot => slot.isAvailable),
        notes,
      };

      return await this.updateAvailability(availabilityId, data);
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error updating time slots:', error);
      throw error;
    }
  }

  /**
   * Get staff availability for specific date
   */
  async getStaffAvailabilityForDate(
    staffSalonAccessId: string,
    date: Date
  ): Promise<StaffAvailability | null> {
    try {
      const filters: AvailabilitySearchFilters = {
        staffSalonAccessId,
        date,
        status: AvailabilityStatus.ACTIVE,
      };

      const result = await this.getAvailability(filters);
      
      // Return the first matching availability (should be unique for staff + date)
      return result.data.length > 0 ? result.data[0] : null;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error getting staff availability for date:', error);
      throw error;
    }
  }

  /**
   * Get staff regular schedule for day of week
   */
  async getStaffRegularSchedule(
    staffSalonAccessId: string,
    dayOfWeek: string
  ): Promise<StaffAvailability | null> {
    try {
      const filters: AvailabilitySearchFilters = {
        staffSalonAccessId,
        dayOfWeek,
        type: AvailabilityType.REGULAR,
        status: AvailabilityStatus.ACTIVE,
      };

      const result = await this.getAvailability(filters);
      
      return result.data.length > 0 ? result.data[0] : null;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error getting regular schedule:', error);
      throw error;
    }
  }

  /**
   * Block specific time slots for a date
   */
  async blockTimeSlots(
    staffSalonAccessId: string,
    date: Date,
    blockedSlots: { startTime: string; endTime: string }[],
    notes?: string
  ): Promise<StaffAvailability> {
    try {
      const data = {
        staffSalonAccessId,
        date: date.toISOString().split('T')[0], // Format as YYYY-MM-DD
        blockedSlots,
        notes,
      };

      const result = await apiService.post<StaffAvailability>('/staff-availability/block', data);
      return result.data;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error blocking time slots:', error);
      throw error;
    }
  }

  /**
   * Unblock specific time slots for a date
   */
  async unblockTimeSlots(
    staffSalonAccessId: string,
    date: Date,
    unblockedSlots: { startTime: string; endTime: string }[],
    notes?: string
  ): Promise<StaffAvailability> {
    try {
      const data = {
        staffSalonAccessId,
        date: date.toISOString().split('T')[0], // Format as YYYY-MM-DD
        unblockedSlots,
        notes,
      };

      const result = await apiService.post<StaffAvailability>('/staff-availability/unblock', data);
      return result.data;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error unblocking time slots:', error);
      throw error;
    }
  }


}

export const staffAvailabilityService = new StaffAvailabilityService();
