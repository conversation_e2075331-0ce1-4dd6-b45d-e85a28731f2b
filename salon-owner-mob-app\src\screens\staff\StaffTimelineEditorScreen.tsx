import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  Alert,
  Modal,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { LinearGradient } from 'react-native-linear-gradient';

import { DarkTitle, DarkBody, WhiteTitle } from '../../components/common/Typography';
import { Button } from '../../components/common/Button';
import { Colors, Gradients } from '../../constants/colors';
import { useToast } from '../../context/ToastContext';
import { useAuth } from '../../context/AuthContext';
import {
  StaffAvailability,
  TimeSlot,
  formatTime,
} from '../../types/staffAvailability';
import { staffAvailabilityService } from '../../services/staffAvailabilityService';

const { width, height } = Dimensions.get('window');

type RootStackParamList = {
  StaffTimelineEditor: {
    availability: StaffAvailability | null;
    staffName: string;
    selectedDate: Date;
    staffSalonAccessId: string;
  };
};

type StaffTimelineEditorScreenRouteProp = RouteProp<RootStackParamList, 'StaffTimelineEditor'>;
type StaffTimelineEditorScreenNavigationProp = StackNavigationProp<RootStackParamList>;

interface TimeSlotBlock {
  id: string;
  startTime: string;
  endTime: string;
  isAvailable: boolean;
  isSelected: boolean;
  isBreakTime: boolean;
  displayTime: string;
  hour: number;
  minutes: number;
}

export default function StaffTimelineEditorScreen() {
  const route = useRoute<StaffTimelineEditorScreenRouteProp>();
  const navigation = useNavigation<StaffTimelineEditorScreenNavigationProp>();
  const { showToast } = useToast();
  const { user } = useAuth();

  const { availability, staffName, selectedDate, staffSalonAccessId } = route.params;

  const [timeSlotBlocks, setTimeSlotBlocks] = useState<TimeSlotBlock[]>([]);
  const [selectedSlots, setSelectedSlots] = useState<string[]>([]);
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [hasChanges, setHasChanges] = useState(false);
  const [currentDate, setCurrentDate] = useState<Date>(selectedDate);
  const [showDatePicker, setShowDatePicker] = useState(false);
  const [isLoadingDate, setIsLoadingDate] = useState(false);

  useEffect(() => {
    generateTimeSlotBlocks();
  }, [availability]);

  const generateTimeSlotBlocks = (sourceAvailability?: StaffAvailability) => {
    try {
      const blocks: TimeSlotBlock[] = [];
      const targetAvailability = sourceAvailability || availability;
      console.log('🔍 generateTimeSlotBlocks:', { sourceAvailability, availability, targetAvailability });

      // Always use full working day (9 AM - 5 PM) to show complete timeline
      let earliestHour = 9;
      let latestHour = 17;

      console.log('📅 StaffTimelineEditor: Generating complete working day timeline from', earliestHour, 'to', latestHour);
      
      // Generate 15-minute blocks for each hour
      for (let hour = earliestHour; hour < latestHour; hour++) {
        for (let minutes = 0; minutes < 60; minutes += 15) {
          const startTime = `${hour.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`;
          const endMinutes = minutes + 15;
          const endHour = endMinutes >= 60 ? hour + 1 : hour;
          const endMin = endMinutes >= 60 ? 0 : endMinutes;
          const endTime = `${endHour.toString().padStart(2, '0')}:${endMin.toString().padStart(2, '0')}`;
          
          // Check if this block is currently available
          // Check if this specific time slot is blocked in the availability data
          let isCurrentlyAvailable = true; // Default to available
          let isCurrentlyBreak = false; // Default to no break

          if (targetAvailability && targetAvailability.timeSlots && targetAvailability.timeSlots.length > 0) {
            // Find if this exact time slot exists in the availability data and is blocked
            const existingSlot = targetAvailability.timeSlots.find(slot =>
              slot.startTime === startTime && slot.endTime === endTime
            );

            if (existingSlot) {
              isCurrentlyAvailable = existingSlot.isAvailable;
              isCurrentlyBreak = !existingSlot.isAvailable;
            }
            // If slot doesn't exist in availability data, it remains available (default)
          }
          
          blocks.push({
            id: `${hour}-${minutes}`,
            startTime,
            endTime,
            isAvailable: isCurrentlyAvailable,
            isSelected: false,
            isBreakTime: isCurrentlyBreak,
            displayTime: `${formatTime(startTime)} - ${formatTime(endTime)}`,
            hour,
            minutes,
          });
        }
      }
      
      setTimeSlotBlocks(blocks);
      console.log('✅ StaffTimelineEditorScreen: Generated', blocks.length, '15-minute blocks');
    } catch (error: any) {
      console.error('❌ StaffTimelineEditorScreen: Error generating blocks:', error);
      showToast('Failed to generate timeline blocks', 'error');
    }
  };

  // Helper function to convert time to minutes for calculations
  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  const handleSlotPress = (block: TimeSlotBlock) => {
    if (!isSelectionMode) {
      setIsSelectionMode(true);
    }

    setSelectedSlots(prev => {
      const isSelected = prev.includes(block.id);
      if (isSelected) {
        return prev.filter(id => id !== block.id);
      } else {
        return [...prev, block.id];
      }
    });

    setTimeSlotBlocks(prev => 
      prev.map(slot => 
        slot.id === block.id 
          ? { ...slot, isSelected: !slot.isSelected }
          : slot
      )
    );

    setHasChanges(true);
  };

  const handleBlockSelected = async () => {
    if (selectedSlots.length === 0) {
      showToast('Please select time slots to block', 'warning');
      return;
    }

    const selectedCount = selectedSlots.length;
    const totalMinutes = selectedCount * 15;
    const hours = Math.floor(totalMinutes / 60);
    const minutes = totalMinutes % 60;

    let timeText = '';
    if (hours > 0) {
      timeText += `${hours} hour${hours > 1 ? 's' : ''}`;
    }
    if (minutes > 0) {
      if (timeText) timeText += ' ';
      timeText += `${minutes} minute${minutes > 1 ? 's' : ''}`;
    }

    Alert.alert(
      'Block Time Slots',
      `Block ${selectedCount} time slot${selectedCount > 1 ? 's' : ''} (${timeText})?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Block',
          style: 'destructive',
          onPress: async () => {
            try {
              // Get ALL blocked slots (existing + newly selected)
              const allBlockedSlots = timeSlotBlocks
                .filter(slot => !slot.isAvailable || selectedSlots.includes(slot.id))
                .map(slot => ({
                  startTime: slot.startTime,
                  endTime: slot.endTime,
                  isAvailable: false,
                  notes: selectedSlots.includes(slot.id) ? 'Newly blocked' : (slot.isBreakTime ? 'Break time' : 'Previously blocked')
                }));

              console.log('🔄 Blocking slots - Total blocked slots to save:', allBlockedSlots.length);
              console.log('📝 Newly selected slots:', selectedCount);
              console.log('📝 Previously blocked slots:', allBlockedSlots.length - selectedCount);

              // Use staffSalonAccessId from route params (always available)
              if (!staffSalonAccessId) {
                showToast('Unable to save: Missing staff information', 'error');
                return;
              }

              // Save ALL blocked slots to database (existing + new)
              await staffAvailabilityService.blockTimeSlots(
                staffSalonAccessId,
                currentDate,
                allBlockedSlots,
                `Added ${selectedCount} new blocked slot${selectedCount > 1 ? 's' : ''} by ${user?.firstName || 'admin'}`
              );

              // Update local state
              setTimeSlotBlocks(prev =>
                prev.map(slot =>
                  selectedSlots.includes(slot.id)
                    ? { ...slot, isAvailable: false, isSelected: false }
                    : slot
                )
              );
              setSelectedSlots([]);
              setIsSelectionMode(false);
              setHasChanges(true);

              showToast(`Blocked ${selectedCount} time slot${selectedCount > 1 ? 's' : ''} and saved to database`, 'success');
            } catch (error: any) {
              console.error('❌ Error blocking time slots:', error);
              showToast('Failed to block time slots. Please try again.', 'error');
            }
          },
        },
      ]
    );
  };

  const handleUnblockSelected = async () => {
    if (selectedSlots.length === 0) {
      showToast('Please select time slots to unblock', 'warning');
      return;
    }

    const selectedCount = selectedSlots.length;

    Alert.alert(
      'Unblock Time Slots',
      `Unblock ${selectedCount} time slot${selectedCount > 1 ? 's' : ''}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Unblock',
          onPress: async () => {
            try {
              // Get the slots that will remain blocked (excluding the ones being unblocked)
              const remainingBlockedSlots = timeSlotBlocks
                .filter(slot => {
                  // Keep slot blocked if:
                  // 1. It's not selected for unblocking, AND
                  // 2. It's currently blocked (not available)
                  return !selectedSlots.includes(slot.id) && !slot.isAvailable;
                })
                .map(slot => ({
                  startTime: slot.startTime,
                  endTime: slot.endTime,
                  isAvailable: false,
                  notes: slot.isBreakTime ? 'Break time' : 'Blocked'
                }));

              // Get the slots being unblocked (for logging)
              const slotsBeingUnblocked = timeSlotBlocks
                .filter(slot => selectedSlots.includes(slot.id) && !slot.isAvailable && !slot.isBreakTime);

              if (slotsBeingUnblocked.length === 0) {
                showToast('Please select blocked time slots to unblock', 'warning');
                return;
              }

              console.log('🔄 Unblocking slots - Remaining blocked slots:', remainingBlockedSlots.length);
              console.log('📝 Slots being unblocked:', slotsBeingUnblocked.length);

              // Use staffSalonAccessId from route params (always available)
              if (!staffSalonAccessId) {
                showToast('Unable to save: Missing staff information', 'error');
                return;
              }

              // Use unblockTimeSlots method with the slots being unblocked
              const slotsToUnblock = slotsBeingUnblocked.map(slot => ({
                startTime: slot.startTime,
                endTime: slot.endTime,
              }));

              await staffAvailabilityService.unblockTimeSlots(
                staffSalonAccessId,
                currentDate,
                slotsToUnblock,
                `Unblocked ${slotsBeingUnblocked.length} time slot${slotsBeingUnblocked.length > 1 ? 's' : ''} by ${user?.firstName || 'admin'}`
              );

              // Update local state
              setTimeSlotBlocks(prev =>
                prev.map(slot =>
                  selectedSlots.includes(slot.id) && !slot.isBreakTime
                    ? { ...slot, isAvailable: true, isSelected: false }
                    : slot
                )
              );
              setSelectedSlots([]);
              setIsSelectionMode(false);
              setHasChanges(true);

              showToast(`Unblocked ${slotsBeingUnblocked.length} time slot${slotsBeingUnblocked.length > 1 ? 's' : ''} and saved to database`, 'success');
            } catch (error: any) {
              console.error('❌ Error unblocking time slots:', error);
              showToast('Failed to unblock time slots. Please try again.', 'error');
            }
          },
        },
      ]
    );
  };

  const handleClearSelection = () => {
    setSelectedSlots([]);
    setTimeSlotBlocks(prev => 
      prev.map(slot => ({ ...slot, isSelected: false }))
    );
    setIsSelectionMode(false);
  };

  const handleSaveChanges = () => {
    if (!hasChanges) {
      navigation.goBack();
      return;
    }

    Alert.alert(
      'Save Changes',
      'All changes have been saved automatically. Return to timeline view?',
      [
        { text: 'Stay Here', style: 'cancel' },
        {
          text: 'Go Back',
          onPress: () => {
            setHasChanges(false);
            navigation.goBack();
          },
        },
      ]
    );
  };

  const handleDateChange = async (newDate: Date) => {
    try {
      setIsLoadingDate(true);
      setCurrentDate(newDate);

      // Load availability for the new date
      const dateAvailability = await staffAvailabilityService.getStaffAvailabilityForDate(
        staffSalonAccessId,
        newDate
      );

      if (dateAvailability) {
        // Use the date-specific availability
        generateTimeSlotBlocks(dateAvailability);
      } else {
        // Use regular schedule as base
        generateTimeSlotBlocks(availability || undefined);
      }

      // Clear selections when changing date
      setSelectedSlots([]);
      setIsSelectionMode(false);
      setHasChanges(false);

      showToast(`Loaded availability for ${newDate.toLocaleDateString()}`, 'info');
    } catch (error: any) {
      console.error('❌ Error loading availability for date:', error);
      showToast('Failed to load availability for selected date', 'error');
    } finally {
      setIsLoadingDate(false);
      setShowDatePicker(false);
    }
  };

  const generateDateOptions = () => {
    const dates = [];
    const today = new Date();

    // Generate next 30 days
    for (let i = 0; i < 30; i++) {
      const date = new Date(today);
      date.setDate(today.getDate() + i);
      dates.push(date);
    }

    return dates;
  };

  const getSlotStyle = (block: TimeSlotBlock) => {
    if (block.isSelected) {
      return [styles.timeSlotBlock, styles.selectedBlock];
    }
    if (block.isBreakTime) {
      return [styles.timeSlotBlock, styles.breakBlock];
    }
    if (block.isAvailable) {
      return [styles.timeSlotBlock, styles.availableBlock];
    }
    return [styles.timeSlotBlock, styles.blockedBlock];
  };

  const getSlotIcon = (block: TimeSlotBlock) => {
    if (block.isSelected) return 'check-circle';
    if (block.isBreakTime) return 'coffee';
    if (block.isAvailable) return 'check';
    return 'block';
  };

  const getSlotIconColor = (block: TimeSlotBlock) => {
    if (block.isSelected) return Colors.white;
    if (block.isBreakTime) return Colors.warning;
    if (block.isAvailable) return Colors.success;
    return Colors.error;
  };

  const renderHourGroup = (hour: number) => {
    const hourBlocks = timeSlotBlocks.filter(block => block.hour === hour);
    if (hourBlocks.length === 0) return null;

    return (
      <View key={hour} style={styles.hourGroup}>
        <View style={styles.hourHeader}>
          <DarkTitle level={4} style={styles.hourTitle}>
            {hour % 12 || 12} {hour >= 12 ? 'PM' : 'AM'}
          </DarkTitle>
          <DarkBody size="small" style={styles.hourSubtitle}>
            {hourBlocks.filter(b => b.isAvailable).length}/4 slots available
          </DarkBody>
        </View>
        
        <View style={styles.slotsGrid}>
          {hourBlocks.map(block => (
            <TouchableOpacity
              key={block.id}
              style={getSlotStyle(block)}
              onPress={() => handleSlotPress(block)}
              activeOpacity={0.7}
            >
              <Icon 
                name={getSlotIcon(block)} 
                size={16} 
                color={getSlotIconColor(block)} 
              />
              <DarkBody size="small" style={[
                styles.slotTime,
                block.isSelected && styles.selectedSlotText,
                block.isBreakTime && styles.breakSlotText,
                !block.isAvailable && !block.isBreakTime && styles.blockedSlotText,
              ]}>
                :{block.minutes.toString().padStart(2, '0')}
              </DarkBody>
            </TouchableOpacity>
          ))}
        </View>
      </View>
    );
  };

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Gradients.primaryButton}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color={Colors.white} />
          </TouchableOpacity>
          <WhiteTitle level={3} style={styles.headerTitle}>
            Edit Timeline
          </WhiteTitle>
          <TouchableOpacity onPress={handleSaveChanges} disabled={!hasChanges}>
            <Icon 
              name="save" 
              size={24} 
              color={hasChanges ? Colors.white : Colors.white + '50'} 
            />
          </TouchableOpacity>
        </View>
      </LinearGradient>

      {/* Staff Info */}
      <View style={styles.staffInfo}>
        <DarkTitle level={4}>{staffName}</DarkTitle>
        <DarkBody size="medium" style={styles.dayInfo}>
          {availability?.displayDate || selectedDate.toLocaleDateString()} - Edit 15-minute time slots
        </DarkBody>

        {/* Date Selector */}
        <TouchableOpacity
          style={styles.dateSelector}
          onPress={() => setShowDatePicker(true)}
          disabled={isLoadingDate}
        >
          <Icon name="calendar-today" size={20} color={Colors.primary} />
          <DarkBody size="medium" style={styles.dateSelectorText}>
            {currentDate.toLocaleDateString('en-US', {
              weekday: 'short',
              month: 'short',
              day: 'numeric',
              year: 'numeric'
            })}
          </DarkBody>
          <Icon name="expand-more" size={20} color={Colors.gray400} />
        </TouchableOpacity>
      </View>

      {/* Selection Controls */}
      {isSelectionMode && (
        <View style={styles.selectionControls}>
          <View style={styles.selectionInfo}>
            <DarkBody size="medium" style={styles.selectionText}>
              {selectedSlots.length} slot{selectedSlots.length !== 1 ? 's' : ''} selected
            </DarkBody>
            <TouchableOpacity onPress={handleClearSelection}>
              <DarkBody size="small" style={styles.clearButton}>
                Clear
              </DarkBody>
            </TouchableOpacity>
          </View>
          
          <View style={styles.actionButtons}>
            <Button
              title="Block Selected"
              onPress={handleBlockSelected}
              style={styles.blockButton}
              disabled={selectedSlots.length === 0}
            />
            <Button
              title="Unblock Selected"
              onPress={handleUnblockSelected}
              style={styles.unblockButton}
              disabled={selectedSlots.length === 0}
            />
          </View>
        </View>
      )}

      {/* Timeline Content */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        <View style={styles.instructionsCard}>
          <Icon name="info" size={20} color={Colors.primary} />
          <View style={styles.instructionsText}>
            <DarkBody size="small" style={styles.instructionsTitle}>
              Tap 15-minute slots to select/deselect them
            </DarkBody>
            <DarkBody size="small" style={styles.instructionsSubtitle}>
              • Green: Available • Red: Blocked • Orange: Break Time
            </DarkBody>
          </View>
        </View>

        <View style={styles.timelineContainer}>
          {Array.from(new Set(timeSlotBlocks.map(block => block.hour)))
            .sort((a, b) => a - b)
            .map(renderHourGroup)}
        </View>
      </ScrollView>

      {/* Date Picker Modal */}
      <Modal
        visible={showDatePicker}
        transparent={true}
        animationType="slide"
        onRequestClose={() => setShowDatePicker(false)}
      >
        <View style={styles.modalOverlay}>
          <View style={styles.datePickerModal}>
            <View style={styles.modalHeader}>
              <DarkTitle level={4}>Select Date</DarkTitle>
              <TouchableOpacity onPress={() => setShowDatePicker(false)}>
                <Icon name="close" size={24} color={Colors.gray600} />
              </TouchableOpacity>
            </View>

            <ScrollView style={styles.dateList}>
              {generateDateOptions().map((date, index) => (
                <TouchableOpacity
                  key={index}
                  style={[
                    styles.dateOption,
                    date.toDateString() === currentDate.toDateString() && styles.selectedDateOption
                  ]}
                  onPress={() => handleDateChange(date)}
                >
                  <View style={styles.dateOptionContent}>
                    <DarkBody size="medium" style={[
                      styles.dateOptionText,
                      date.toDateString() === currentDate.toDateString() && styles.selectedDateText
                    ]}>
                      {date.toLocaleDateString('en-US', {
                        weekday: 'long',
                        month: 'long',
                        day: 'numeric',
                        year: 'numeric'
                      })}
                    </DarkBody>
                    {index === 0 && (
                      <DarkBody size="small" style={styles.todayLabel}>
                        Today
                      </DarkBody>
                    )}
                    {index === 1 && (
                      <DarkBody size="small" style={styles.tomorrowLabel}>
                        Tomorrow
                      </DarkBody>
                    )}
                  </View>
                  {date.toDateString() === currentDate.toDateString() && (
                    <Icon name="check" size={20} color={Colors.primary} />
                  )}
                </TouchableOpacity>
              ))}
            </ScrollView>
          </View>
        </View>
      </Modal>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    paddingTop: height * 0.06,
    paddingBottom: height * 0.02,
    paddingHorizontal: width * 0.05,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    marginHorizontal: width * 0.05,
  },
  staffInfo: {
    padding: width * 0.05,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  dayInfo: {
    color: Colors.gray600,
    marginTop: height * 0.005,
  },
  dateSelector: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginTop: height * 0.015,
    padding: width * 0.03,
    backgroundColor: Colors.gray50,
    borderRadius: width * 0.02,
    borderWidth: 1,
    borderColor: Colors.gray200,
  },
  dateSelectorText: {
    flex: 1,
    marginLeft: width * 0.02,
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  selectionControls: {
    backgroundColor: Colors.primary + '10',
    padding: width * 0.05,
    borderBottomWidth: 1,
    borderBottomColor: Colors.primary + '20',
  },
  selectionInfo: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: height * 0.015,
  },
  selectionText: {
    color: Colors.primary,
    fontWeight: '600',
  },
  clearButton: {
    color: Colors.primary,
    textDecorationLine: 'underline',
  },
  actionButtons: {
    flexDirection: 'row',
    gap: width * 0.03,
  },
  actionButton: {
    flex: 1,
    paddingVertical: height * 0.012,
  },
  blockButton: {
    backgroundColor: Colors.error,
  },
  unblockButton: {
    backgroundColor: Colors.success,
  },
  content: {
    flex: 1,
  },
  instructionsCard: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    backgroundColor: Colors.primary + '10',
    margin: width * 0.05,
    padding: width * 0.04,
    borderRadius: width * 0.03,
    borderWidth: 1,
    borderColor: Colors.primary + '20',
  },
  instructionsText: {
    flex: 1,
    marginLeft: width * 0.03,
  },
  instructionsTitle: {
    color: Colors.primary,
    fontWeight: '600',
    marginBottom: height * 0.005,
  },
  instructionsSubtitle: {
    color: Colors.primary,
    opacity: 0.8,
  },
  timelineContainer: {
    padding: width * 0.05,
    paddingTop: 0,
  },
  hourGroup: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.03,
    padding: width * 0.04,
    marginBottom: height * 0.02,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  hourHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: height * 0.015,
    paddingBottom: height * 0.01,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  hourTitle: {
    color: Colors.textPrimary,
  },
  hourSubtitle: {
    color: Colors.gray600,
  },
  slotsGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: width * 0.02,
  },
  timeSlotBlock: {
    width: (width - width * 0.1 - width * 0.08 - width * 0.06) / 4, // 4 slots per row with gaps
    aspectRatio: 1,
    borderRadius: width * 0.02,
    alignItems: 'center',
    justifyContent: 'center',
    borderWidth: 2,
    gap: height * 0.005,
  },
  availableBlock: {
    backgroundColor: Colors.success + '20',
    borderColor: Colors.success,
  },
  blockedBlock: {
    backgroundColor: Colors.error + '20',
    borderColor: Colors.error,
  },
  breakBlock: {
    backgroundColor: Colors.warning + '20',
    borderColor: Colors.warning,
  },
  selectedBlock: {
    backgroundColor: Colors.primary,
    borderColor: Colors.primary,
  },
  slotTime: {
    fontSize: 11,
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  selectedSlotText: {
    color: Colors.white,
  },
  breakSlotText: {
    color: Colors.warning,
  },
  blockedSlotText: {
    color: Colors.error,
  },
  // Modal styles
  modalOverlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    justifyContent: 'flex-end',
  },
  datePickerModal: {
    backgroundColor: Colors.white,
    borderTopLeftRadius: width * 0.05,
    borderTopRightRadius: width * 0.05,
    maxHeight: height * 0.7,
  },
  modalHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: width * 0.05,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  dateList: {
    maxHeight: height * 0.5,
  },
  dateOption: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    padding: width * 0.05,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray100,
  },
  selectedDateOption: {
    backgroundColor: Colors.primary + '10',
  },
  dateOptionContent: {
    flex: 1,
  },
  dateOptionText: {
    color: Colors.textPrimary,
    fontWeight: '500',
  },
  selectedDateText: {
    color: Colors.primary,
    fontWeight: '600',
  },
  todayLabel: {
    color: Colors.success,
    fontWeight: '600',
    marginTop: 2,
  },
  tomorrowLabel: {
    color: Colors.warning,
    fontWeight: '600',
    marginTop: 2,
  },
});
