import { Router } from 'express';
import { bookingController } from '../controllers/bookingController';

import { protect, authorize } from '../middlewares/authMiddleware';
import { body, query, param } from 'express-validator';

const router = Router();

router.use(protect);

// Validation schemas
const createBookingValidation = [
  body('salonId').isUUID().withMessage('Valid salon ID is required'),
  body('customerId').isUUID().withMessage('Valid customer ID is required'),
  body('serviceId').isUUID().withMessage('Valid service ID is required'),
  body('staffSalonAccessId').isUUID().withMessage('Valid staff salon access ID is required'),
  body('bookingDate').isISO8601().withMessage('Valid booking date is required (YYYY-MM-DD)'),
  body('startTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Valid start time is required (HH:mm)'),
  body('endTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Valid end time is required (HH:mm)'),
  body('notes').optional().isString().isLength({ max: 1000 }).withMessage('Notes must be a string with max 1000 characters'),
  body('specialRequests').optional().isString().isLength({ max: 1000 }).withMessage('Special requests must be a string with max 1000 characters'),
  body('offerId').optional().isUUID().withMessage('Valid offer ID is required if provided'),
];

const updateBookingStatusValidation = [
  param('bookingId').isUUID().withMessage('Valid booking ID is required'),
  body('status').isIn(['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW']).withMessage('Valid status is required'),
  body('notes').optional().isString().isLength({ max: 1000 }).withMessage('Notes must be a string with max 1000 characters'),
];

const cancelBookingValidation = [
  param('bookingId').isUUID().withMessage('Valid booking ID is required'),
  body('cancellationReason').isString().isLength({ min: 1, max: 500 }).withMessage('Cancellation reason is required (max 500 characters)'),
];

const rescheduleBookingValidation = [
  param('bookingId').isUUID().withMessage('Valid booking ID is required'),
  body('bookingDate').isISO8601().withMessage('Valid booking date is required (YYYY-MM-DD)'),
  body('startTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Valid start time is required (HH:mm)'),
  body('staffSalonAccessId').optional().isUUID().withMessage('Valid staff salon access ID is required if provided'),
];

const availabilityValidation = [
  query('salonId').isUUID().withMessage('Valid salon ID is required'),
  query('serviceId').isUUID().withMessage('Valid service ID is required'),
  query('date').isISO8601().withMessage('Valid date is required (YYYY-MM-DD)'),
  query('preferredStaffId').optional().isUUID().withMessage('Valid staff ID is required if provided'),
];

const bookingFiltersValidation = [
  query('salonId').optional().isUUID().withMessage('Valid salon ID is required if provided'),
  query('customerId').optional().isUUID().withMessage('Valid customer ID is required if provided'),
  query('serviceId').optional().isUUID().withMessage('Valid service ID is required if provided'),
  query('staffSalonAccessId').optional().isUUID().withMessage('Valid staff salon access ID is required if provided'),
  query('status').optional().isIn(['PENDING', 'CONFIRMED', 'IN_PROGRESS', 'COMPLETED', 'CANCELLED', 'NO_SHOW']).withMessage('Valid status is required if provided'),
  query('paymentStatus').optional().isIn(['PENDING', 'PAID', 'PARTIALLY_PAID', 'REFUNDED', 'FAILED']).withMessage('Valid payment status is required if provided'),
  query('dateFrom').optional().isISO8601().withMessage('Valid date from is required if provided (YYYY-MM-DD)'),
  query('dateTo').optional().isISO8601().withMessage('Valid date to is required if provided (YYYY-MM-DD)'),
  query('search').optional().isString().isLength({ max: 100 }).withMessage('Search term must be a string with max 100 characters'),
  query('sortBy').optional().isIn(['bookingDate', 'createdAt', 'finalPrice', 'status']).withMessage('Valid sort field is required if provided'),
  query('sortOrder').optional().isIn(['ASC', 'DESC']).withMessage('Valid sort order is required if provided'),
  query('limit').optional().isInt({ min: 1, max: 100 }).withMessage('Limit must be between 1 and 100'),
  query('offset').optional().isInt({ min: 0 }).withMessage('Offset must be 0 or greater'),
];

const checkAvailabilityValidation = [
  query('salonId').isUUID().withMessage('Valid salon ID is required'),
  query('serviceId').isUUID().withMessage('Valid service ID is required'),
  query('staffId').isUUID().withMessage('Valid staff ID is required'),
  query('date').isISO8601().withMessage('Valid date is required (YYYY-MM-DD)'),
  query('startTime').matches(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).withMessage('Valid start time is required (HH:mm)'),
];

const bookingStatsValidation = [
  query('salonId').isUUID().withMessage('Valid salon ID is required'),
  query('dateFrom').optional().isISO8601().withMessage('Valid date from is required if provided (YYYY-MM-DD)'),
  query('dateTo').optional().isISO8601().withMessage('Valid date to is required if provided (YYYY-MM-DD)'),
];

// Routes

/**
 * @route   GET /api/v1/bookings/availability
 * @desc    Get availability for a service on a specific date
 * @access  Private
 */
router.get(
  '/availability',
  availabilityValidation,
  bookingController.getAvailability
);

/**
 * @route   POST /api/v1/bookings
 * @desc    Create a new booking
 * @access  Private
 */
router.post(
  '/',
  createBookingValidation,
  bookingController.createBooking
);

/**
 * @route   GET /api/v1/bookings
 * @desc    Get bookings with filters
 * @access  Private
 */
router.get(
  '/',
  bookingFiltersValidation,
  bookingController.getBookings
);

/**
 * @route   GET /api/v1/bookings/salon
 * @desc    Get salon bookings
 * @access  Private
 */
router.get(
  '/salon',
  bookingFiltersValidation,
  bookingController.getSalonBookings
);

/**
 * @route   GET /api/v1/bookings/stats
 * @desc    Get booking statistics
 * @access  Private
 */
router.get(
  '/stats',
  bookingStatsValidation,
  bookingController.getBookingStats
);

/**
 * @route   GET /api/v1/bookings/check-availability
 * @desc    Check if a specific time slot is available
 * @access  Private
 */
router.get(
  '/check-availability',
  checkAvailabilityValidation,
  bookingController.checkSlotAvailability
);

/**
 * @route   GET /api/v1/bookings/:bookingId
 * @desc    Get booking by ID
 * @access  Private
 */
router.get(
  '/:bookingId',
  [param('bookingId').isUUID().withMessage('Valid booking ID is required')],
  bookingController.getBookingById
);

/**
 * @route   PATCH /api/v1/bookings/:bookingId/status
 * @desc    Update booking status
 * @access  Private
 */
router.patch(
  '/:bookingId/status',
  updateBookingStatusValidation,
  bookingController.updateBookingStatus
);

/**
 * @route   PATCH /api/v1/bookings/:bookingId/cancel
 * @desc    Cancel booking
 * @access  Private
 */
router.patch(
  '/:bookingId/cancel',
  cancelBookingValidation,
  bookingController.cancelBooking
);

/**
 * @route   PATCH /api/v1/bookings/:bookingId/reschedule
 * @desc    Reschedule booking
 * @access  Private
 */
router.patch(
  '/:bookingId/reschedule',
  rescheduleBookingValidation,
  bookingController.rescheduleBooking
);

export default router;
