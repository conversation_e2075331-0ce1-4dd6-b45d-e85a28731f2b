import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { RouteProp, useRoute, useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { LinearGradient } from 'react-native-linear-gradient';

import { DarkTitle, DarkBody, WhiteTitle } from '../../components/common/Typography';
import { Colors, Gradients } from '../../constants/colors';
import { useToast } from '../../context/ToastContext';
import {
  StaffAvailability,
  TimeSlot,
  formatTime,
  getAvailabilityTypeColor,
  getAvailabilityStatusColor,
} from '../../types/staffAvailability';
import { staffAvailabilityService } from '../../services/staffAvailabilityService';

const { width, height } = Dimensions.get('window');

type RootStackParamList = {
  StaffTimeline: {
    availability: StaffAvailability | null;
    staffName: string;
    selectedDate?: Date;
    staffSalonAccessId: string;
  };
  StaffTimelineEditor: {
    availability: StaffAvailability | null;
    staffName: string;
    selectedDate: Date;
    staffSalonAccessId: string;
  };
};

type StaffTimelineScreenRouteProp = RouteProp<RootStackParamList, 'StaffTimeline'>;
type StaffTimelineScreenNavigationProp = StackNavigationProp<RootStackParamList>;

interface HourlySlot {
  hour: number;
  displayHour: string;
  totalMinutes: number;
  availableMinutes: number;
  availabilityPercentage: number;
  timeSlots: TimeSlot[];
  isFullyAvailable: boolean;
  isPartiallyAvailable: boolean;
  isUnavailable: boolean;
}

export default function StaffTimelineScreen() {
  const route = useRoute<StaffTimelineScreenRouteProp>();
  const navigation = useNavigation<StaffTimelineScreenNavigationProp>();
  const { showToast } = useToast();

  const { availability, staffName, selectedDate, staffSalonAccessId } = route.params;

  const [loading, setLoading] = useState(false);
  const [refreshing, setRefreshing] = useState(false);
  const [hourlyBreakdown, setHourlyBreakdown] = useState<HourlySlot[]>([]);
  const [currentAvailability, setCurrentAvailability] = useState<StaffAvailability | null>(availability);

  useEffect(() => {
    generateHourlyBreakdown();
  }, [currentAvailability]);

  // Refresh data when screen comes into focus (e.g., returning from editor)
  useFocusEffect(
    React.useCallback(() => {
      console.log('🔄 StaffTimelineScreen: Screen focused, refreshing data');
      refreshAvailabilityData();
    }, [selectedDate, staffSalonAccessId])
  );

  const refreshAvailabilityData = async () => {
    try {
      console.log('📡 Fetching fresh availability data for date:', selectedDate?.toLocaleDateString());

      if (selectedDate && staffSalonAccessId) {
        // Fetch fresh data for the specific date
        const freshAvailability = await staffAvailabilityService.getStaffAvailabilityForDate(
          staffSalonAccessId,
          selectedDate
        );

        console.log('✅ Fresh availability data:', freshAvailability);
        setCurrentAvailability(freshAvailability);
      } else {
        // Use the original availability if no specific date
        setCurrentAvailability(availability);
      }
    } catch (error: any) {
      console.error('❌ Error refreshing availability data:', error);
      // Fall back to original availability on error
      setCurrentAvailability(availability);
    }
  };

  const generateHourlyBreakdown = () => {
    try {
      setLoading(true);

   

      // Find the earliest and latest times from all time slots
      let earliestHour = 24;
      let latestHour = 0;
      let timeSlots = currentAvailability?.timeSlots || [];

      // Always generate full working day timeline (9 AM to 5 PM)
      earliestHour = 9;
      latestHour = 17;

      console.log("📅 Generating full working day timeline from", earliestHour, "to", latestHour);

      // Create complete timeline with all 15-minute slots for the working day
      const fullDayTimeSlots = [];
      for (let hour = earliestHour; hour < latestHour; hour++) {
        for (let minute = 0; minute < 60; minute += 15) {
          const startTime = `${hour.toString().padStart(2, '0')}:${minute.toString().padStart(2, '0')}`;
          const endMinute = minute + 15;
          const endHour = endMinute >= 60 ? hour + 1 : hour;
          const endTime = `${endHour.toString().padStart(2, '0')}:${(endMinute % 60).toString().padStart(2, '0')}`;

          // Check if this slot is blocked in the availability data
          let isAvailable = true; // Default to available
          let notes = 'Available for booking';

          if (currentAvailability && currentAvailability.timeSlots && currentAvailability.timeSlots.length > 0) {
            // Find if this time slot is blocked in the availability data
            const blockedSlot = currentAvailability.timeSlots.find(slot =>
              slot.startTime === startTime && slot.endTime === endTime && !slot.isAvailable
            );

            if (blockedSlot) {
              isAvailable = false;
              notes = blockedSlot.notes || 'Blocked time';
            }
          }

          fullDayTimeSlots.push({
            startTime,
            endTime,
            isAvailable,
            notes
          });
        }
      }

      // Use the full day timeline instead of just the blocked slots
      timeSlots = fullDayTimeSlots;


      
      const hourlySlots: HourlySlot[] = [];
      
      // Generate hourly breakdown
      for (let hour = earliestHour; hour <= latestHour; hour++) {
        const hourStart = `${hour.toString().padStart(2, '0')}:00`;
        const hourEnd = `${(hour + 1).toString().padStart(2, '0')}:00`;
        
        let availableMinutes = 0;
        const overlappingSlots: TimeSlot[] = [];
        
        // Check each time slot for overlap with this hour
        timeSlots.forEach(slot => {
          const slotStart = slot.startTime;
          const slotEnd = slot.endTime;

          // Check if slot overlaps with this hour
          if (isTimeOverlap(slotStart, slotEnd, hourStart, hourEnd)) {
            overlappingSlots.push(slot);

            if (slot.isAvailable) {
              // Calculate overlap minutes for available slots
              const overlapStart = getLatestTime(slotStart, hourStart);
              const overlapEnd = getEarliestTime(slotEnd, hourEnd);
              const overlapMinutes = getTimeDifferenceInMinutes(overlapStart, overlapEnd);

              availableMinutes += overlapMinutes;
            }
            // Note: Break time slots (isAvailable: false) are tracked but don't add to available minutes
          }
        });
        
        // Ensure we don't exceed 60 minutes per hour
        availableMinutes = Math.min(availableMinutes, 60);
        
        const availabilityPercentage = (availableMinutes / 60) * 100;
        
        hourlySlots.push({
          hour,
          displayHour: formatHour(hour),
          totalMinutes: 60,
          availableMinutes,
          availabilityPercentage,
          timeSlots: overlappingSlots,
          isFullyAvailable: availabilityPercentage === 100,
          isPartiallyAvailable: availabilityPercentage > 0 && availabilityPercentage < 100,
          isUnavailable: availabilityPercentage === 0,
        });
      }
      
      setHourlyBreakdown(hourlySlots);
      console.log('✅ StaffTimelineScreen: Generated hourly breakdown:', hourlySlots.length, 'hours');
    } catch (error: any) {
      console.error('❌ StaffTimelineScreen: Error generating breakdown:', error);
      showToast('Failed to generate timeline', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Helper functions
  const formatHour = (hour: number): string => {
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour} ${ampm}`;
  };

  const isTimeOverlap = (start1: string, end1: string, start2: string, end2: string): boolean => {
    const start1Minutes = timeToMinutes(start1);
    const end1Minutes = timeToMinutes(end1);
    const start2Minutes = timeToMinutes(start2);
    const end2Minutes = timeToMinutes(end2);
    
    return start1Minutes < end2Minutes && end1Minutes > start2Minutes;
  };

  const getLatestTime = (time1: string, time2: string): string => {
    return timeToMinutes(time1) > timeToMinutes(time2) ? time1 : time2;
  };

  const getEarliestTime = (time1: string, time2: string): string => {
    return timeToMinutes(time1) < timeToMinutes(time2) ? time1 : time2;
  };

  const timeToMinutes = (time: string): number => {
    const [hours, minutes] = time.split(':').map(Number);
    return hours * 60 + minutes;
  };

  const getTimeDifferenceInMinutes = (startTime: string, endTime: string): number => {
    return timeToMinutes(endTime) - timeToMinutes(startTime);
  };

  const getAvailabilityColor = (percentage: number): string => {
    if (percentage === 0) return Colors.gray300;
    if (percentage < 50) return Colors.warning;
    if (percentage < 100) return Colors.primary;
    return Colors.success;
  };

  const getAvailabilityLabel = (slot: HourlySlot): string => {
    if (slot.isUnavailable) {
      // Check if this hour has break time slots
      const hasBreakSlots = slot.timeSlots.some(timeSlot => !timeSlot.isAvailable);
      if (hasBreakSlots) {
        return 'Break Time';
      }
      return 'Unavailable';
    }
    if (slot.isFullyAvailable) return 'Fully Available';

    // For partial availability, show more details
    const breakMinutes = 60 - slot.availableMinutes;
    if (breakMinutes > 0) {
      return `${slot.availableMinutes}min Work, ${breakMinutes}min Break`;
    }
    return `${slot.availableMinutes}min Available`;
  };

  const getDetailedSlotInfo = (slot: HourlySlot): string => {
    if (slot.timeSlots.length === 0) return 'No schedule';

    const workSlots = slot.timeSlots.filter(s => s.isAvailable);
    const breakSlots = slot.timeSlots.filter(s => !s.isAvailable);

    let info = '';
    if (workSlots.length > 0) {
      info += `Work: ${workSlots.length} slot${workSlots.length > 1 ? 's' : ''}`;
    }
    if (breakSlots.length > 0) {
      if (info) info += ', ';
      info += `Break: ${breakSlots.length} slot${breakSlots.length > 1 ? 's' : ''}`;
    }

    return info || `${slot.timeSlots.length} slot${slot.timeSlots.length > 1 ? 's' : ''}`;
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await refreshAvailabilityData();
    setRefreshing(false);
  };

  const handleEditTimeline = () => {
    console.log('✏️ StaffTimelineScreen: Navigating to timeline editor');

    // Use the selectedDate passed from calendar, or determine from availability
    let editDate = selectedDate || new Date();

    if (!selectedDate && currentAvailability) {
      if (currentAvailability.date) {
        editDate = new Date(currentAvailability.date);
      } else if (currentAvailability.dayOfWeek) {
        // For regular schedule, use today's date or next occurrence of this day
        const today = new Date();
        const dayNames = ['sunday', 'monday', 'tuesday', 'wednesday', 'thursday', 'friday', 'saturday'];
        const targetDay = dayNames.indexOf(currentAvailability.dayOfWeek.toLowerCase());
        const todayDay = today.getDay();

        if (targetDay >= 0) {
          const daysUntilTarget = (targetDay - todayDay + 7) % 7;
          editDate = new Date(today);
          editDate.setDate(today.getDate() + daysUntilTarget);
        }
      }
    }

    console.log('📅 Editing timeline for date:', editDate.toLocaleDateString());

    navigation.navigate('StaffTimelineEditor', {
      availability: currentAvailability,
      staffName,
      selectedDate: editDate,
      staffSalonAccessId,
    });
  };

  const renderHourlySlot = (slot: HourlySlot) => (
    <View key={slot.hour} style={styles.hourlySlot}>
      {/* Hour Label */}
      <View style={styles.hourLabel}>
        <DarkTitle level={4} style={styles.hourText}>
          {slot.displayHour}
        </DarkTitle>
        <DarkBody size="small" style={styles.hourRange}>
          {slot.hour.toString().padStart(2, '0')}:00 - {(slot.hour + 1).toString().padStart(2, '0')}:00
        </DarkBody>
      </View>

      {/* Timeline Bar */}
      <View style={styles.timelineContainer}>
        <View style={styles.timelineBackground}>
          {/* Available time fill */}
          <View
            style={[
              styles.timelineFill,
              {
                width: `${slot.availabilityPercentage}%`,
                backgroundColor: getAvailabilityColor(slot.availabilityPercentage),
              }
            ]}
          />

          {/* Break time indicator (if there are break slots) */}
          {slot.timeSlots.some(s => !s.isAvailable) && slot.availabilityPercentage < 100 && (
            <View
              style={[
                styles.breakTimeFill,
                {
                  left: `${slot.availabilityPercentage}%`,
                  width: `${100 - slot.availabilityPercentage}%`,
                  backgroundColor: Colors.warning + '40',
                }
              ]}
            />
          )}
        </View>

        {/* Percentage Label */}
        <View style={styles.percentageContainer}>
          <DarkBody size="small" style={styles.percentageText}>
            {slot.availabilityPercentage.toFixed(0)}%
          </DarkBody>
        </View>
      </View>

      {/* Availability Info */}
      <View style={styles.availabilityInfo}>
        <View style={styles.availabilityDetails}>
          <DarkBody size="small" style={[
            styles.availabilityLabel,
            { color: getAvailabilityColor(slot.availabilityPercentage) }
          ]}>
            {getAvailabilityLabel(slot)}
          </DarkBody>

          {/* Break time indicator */}
          {slot.timeSlots.some(s => !s.isAvailable) && (
            <View style={styles.breakIndicator}>
              <Icon name="coffee" size={12} color={Colors.warning} />
              <DarkBody size="small" style={styles.breakText}>
                Break
              </DarkBody>
            </View>
          )}
        </View>

        <DarkBody size="small" style={styles.slotsInfo}>
          {getDetailedSlotInfo(slot)}
        </DarkBody>
      </View>
    </View>
  );

  if (loading) {
    return (
      <View style={styles.container}>
        <LinearGradient
          colors={Gradients.primaryButton}
          style={styles.header}
          start={{ x: 0, y: 0 }}
          end={{ x: 1, y: 1 }}
        >
          <View style={styles.headerContent}>
            <TouchableOpacity onPress={() => navigation.goBack()}>
              <Icon name="arrow-back" size={24} color={Colors.white} />
            </TouchableOpacity>
            <WhiteTitle level={3} style={styles.headerTitle}>
              Timeline View
            </WhiteTitle>
            <View style={{ width: 24 }} />
          </View>
        </LinearGradient>
        <View style={styles.loadingContainer}>
          <DarkBody>Generating timeline...</DarkBody>
        </View>
      </View>
    );
  }

  return (
    <View style={styles.container}>
      {/* Header */}
      <LinearGradient
        colors={Gradients.primaryButton}
        style={styles.header}
        start={{ x: 0, y: 0 }}
        end={{ x: 1, y: 1 }}
      >
        <View style={styles.headerContent}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color={Colors.white} />
          </TouchableOpacity>
          <WhiteTitle level={3} style={styles.headerTitle}>
            Timeline View
          </WhiteTitle>
          <View style={styles.headerActions}>
            <TouchableOpacity onPress={handleEditTimeline} style={styles.headerActionButton}>
              <Icon name="edit" size={20} color={Colors.white} />
            </TouchableOpacity>
            <TouchableOpacity onPress={handleRefresh}>
              <Icon name="refresh" size={24} color={Colors.white} />
            </TouchableOpacity>
          </View>
        </View>
      </LinearGradient>

      {/* Staff Info */}
      <View style={styles.staffInfo}>
        <DarkTitle level={4}>{staffName}</DarkTitle>
        <DarkBody size="medium" style={styles.dayInfo}>
          {selectedDate
            ? `${selectedDate.toLocaleDateString('en-US', {
                weekday: 'long',
                year: 'numeric',
                month: 'long',
                day: 'numeric'
              })} - ${currentAvailability?.displayType || 'Default Schedule'}`
            : `${currentAvailability?.displayDate || 'Working Day'} - ${currentAvailability?.displayType || 'Default Schedule'}`
          }
        </DarkBody>

        <DarkBody size="small" style={styles.totalHours}>
          Total Available: {currentAvailability?.totalAvailableHours?.toFixed(1) || '8.0'} hours
        </DarkBody>
        {selectedDate && (
          <DarkBody size="small" style={styles.dateNote}>
            📅 Viewing schedule for selected date
          </DarkBody>
        )}
      </View>

      {/* Timeline Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.primary]}
            tintColor={Colors.primary}
          />
        }
      >
        <View style={styles.timelineList}>
          {hourlyBreakdown.map(renderHourlySlot)}
        </View>

        {/* Summary */}
        <View style={styles.summary}>
          <DarkTitle level={4} style={styles.summaryTitle}>
            Summary
          </DarkTitle>
          <View style={styles.summaryStats}>
            <View style={styles.statItem}>
              <DarkBody size="small" style={styles.statLabel}>Total Hours</DarkBody>
              <DarkTitle level={4} style={styles.statValue}>
                {hourlyBreakdown.length}
              </DarkTitle>
            </View>
            <View style={styles.statItem}>
              <DarkBody size="small" style={styles.statLabel}>Available Hours</DarkBody>
              <DarkTitle level={4} style={[styles.statValue, { color: Colors.success }]}>
                {currentAvailability?.totalAvailableHours?.toFixed(1) || '8.0'}
              </DarkTitle>
            </View>
            <View style={styles.statItem}>
              <DarkBody size="small" style={styles.statLabel}>Avg Availability</DarkBody>
              <DarkTitle level={4} style={[styles.statValue, { color: Colors.primary }]}>
                {hourlyBreakdown.length > 0 
                  ? (hourlyBreakdown.reduce((sum, slot) => sum + slot.availabilityPercentage, 0) / hourlyBreakdown.length).toFixed(0)
                  : 0
                }%
              </DarkTitle>
            </View>
          </View>
        </View>
      </ScrollView>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    paddingTop: height * 0.06,
    paddingBottom: height * 0.02,
    paddingHorizontal: width * 0.05,
  },
  headerContent: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    marginHorizontal: width * 0.05,
  },
  headerActions: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: width * 0.03,
  },
  headerActionButton: {
    padding: width * 0.01,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  staffInfo: {
    padding: width * 0.05,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  dayInfo: {
    color: Colors.gray600,
    marginTop: height * 0.005,
  },
  totalHours: {
    color: Colors.primary,
    fontWeight: '600',
    marginTop: height * 0.005,
  },
  dateNote: {
    color: Colors.gray600,
    fontStyle: 'italic',
    marginTop: height * 0.005,
  },
  content: {
    flex: 1,
  },
  timelineList: {
    padding: width * 0.05,
  },
  hourlySlot: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.03,
    padding: width * 0.04,
    marginBottom: height * 0.015,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  hourLabel: {
    marginBottom: height * 0.015,
  },
  hourText: {
    color: Colors.textPrimary,
    marginBottom: height * 0.002,
  },
  hourRange: {
    color: Colors.gray600,
  },
  timelineContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: height * 0.015,
  },
  timelineBackground: {
    flex: 1,
    height: height * 0.025,
    backgroundColor: Colors.gray200,
    borderRadius: height * 0.0125,
    overflow: 'hidden',
    marginRight: width * 0.03,
  },
  timelineFill: {
    height: '100%',
    borderRadius: height * 0.0125,
  },
  breakTimeFill: {
    position: 'absolute',
    height: '100%',
    borderRadius: height * 0.0125,
    borderWidth: 1,
    borderColor: Colors.warning,
    borderStyle: 'dashed',
  },
  percentageContainer: {
    minWidth: width * 0.12,
    alignItems: 'flex-end',
  },
  percentageText: {
    color: Colors.textPrimary,
    fontWeight: '600',
  },
  availabilityInfo: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
  },
  availabilityDetails: {
    flex: 1,
    marginRight: width * 0.02,
  },
  availabilityLabel: {
    fontWeight: '600',
    marginBottom: height * 0.003,
  },
  breakIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.warning + '20',
    paddingHorizontal: width * 0.015,
    paddingVertical: height * 0.002,
    borderRadius: width * 0.01,
    alignSelf: 'flex-start',
    marginTop: height * 0.003,
  },
  breakText: {
    color: Colors.warning,
    marginLeft: width * 0.005,
    fontSize: 10,
    fontWeight: '600',
  },
  slotsInfo: {
    color: Colors.gray500,
    textAlign: 'right',
    fontSize: 11,
  },
  summary: {
    margin: width * 0.05,
    backgroundColor: Colors.white,
    borderRadius: width * 0.03,
    padding: width * 0.04,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  summaryTitle: {
    marginBottom: height * 0.02,
    color: Colors.textPrimary,
  },
  summaryStats: {
    flexDirection: 'row',
    justifyContent: 'space-around',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    color: Colors.gray600,
    marginBottom: height * 0.005,
  },
  statValue: {
    color: Colors.textPrimary,
  },
});
