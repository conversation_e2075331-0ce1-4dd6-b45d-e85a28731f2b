import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { StaffSalonAccess } from './StaffSalonAccess';
import { Service } from './Service';
import { User } from './User';

export enum AssignmentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
}

export enum SpecializationLevel {
  BEGINNER = 'BEGINNER',
  INTERMEDIATE = 'INTERMEDIATE',
  ADVANCED = 'ADVANCED',
  EXPERT = 'EXPERT',
  MASTER = 'MASTER',
}

@Entity('staff_service_assignments')
@Unique(['staffSalonAccessId', 'serviceId'])
@Index(['staffSalonAccessId'])
@Index(['serviceId'])
@Index(['status'])
@Index(['assignedBy'])
export class StaffServiceAssignment {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'uuid',
    comment: 'Reference to staff salon access record',
  })
  staffSalonAccessId!: string;

  @Column({
    type: 'uuid',
    comment: 'Reference to service',
  })
  serviceId!: string;

  @Column({
    type: 'enum',
    enum: AssignmentStatus,
    default: AssignmentStatus.ACTIVE,
    comment: 'Status of the assignment',
  })
  status!: AssignmentStatus;

  @Column({
    type: 'enum',
    enum: SpecializationLevel,
    default: SpecializationLevel.INTERMEDIATE,
    comment: 'Staff specialization level for this service',
  })
  specializationLevel!: SpecializationLevel;

  @Column({
    type: 'decimal',
    precision: 5,
    scale: 2,
    nullable: true,
    comment: 'Commission percentage for this service (0-100)',
  })
  commissionRate?: number;

  @Column({
    type: 'decimal',
    precision: 10,
    scale: 2,
    nullable: true,
    comment: 'Custom hourly rate for this service',
  })
  customRate?: number;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether staff can be booked for this service',
  })
  isBookable!: boolean;

  @Column({
    type: 'int',
    nullable: true,
    comment: 'Priority order for staff selection (lower = higher priority)',
  })
  priority?: number;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Notes about this assignment',
  })
  notes?: string;

  @Column({
    type: 'timestamp',
    comment: 'When the assignment starts',
  })
  startDate!: Date;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When the assignment ends',
  })
  endDate?: Date;

  @Column({
    type: 'uuid',
    comment: 'ID of the admin who made this assignment',
  })
  assignedBy!: string;

  @Column({
    type: 'timestamp',
    comment: 'When the assignment was made',
  })
  assignedAt!: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'ID of the admin who last modified this assignment',
  })
  lastModifiedBy?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When the assignment was last modified',
  })
  lastModifiedAt?: Date;

  // Relationships
  @ManyToOne(() => StaffSalonAccess, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'staffSalonAccessId' })
  staffSalonAccess!: StaffSalonAccess;

  @ManyToOne(() => Service, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'serviceId' })
  service!: Service;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'assignedBy' })
  assigner!: User;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'lastModifiedBy' })
  lastModifier?: User;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Virtual properties
  get isCurrentlyActive(): boolean {
    if (this.status !== AssignmentStatus.ACTIVE) return false;
    if (this.endDate && this.endDate < new Date()) return false;
    return true;
  }

  get displayStatus(): string {
    switch (this.status) {
      case AssignmentStatus.ACTIVE:
        return this.endDate && this.endDate < new Date() ? 'Expired' : 'Active';
      case AssignmentStatus.INACTIVE:
        return 'Inactive';
      case AssignmentStatus.SUSPENDED:
        return 'Suspended';
      default:
        return 'Unknown';
    }
  }

  get displaySpecialization(): string {
    switch (this.specializationLevel) {
      case SpecializationLevel.BEGINNER:
        return 'Beginner';
      case SpecializationLevel.INTERMEDIATE:
        return 'Intermediate';
      case SpecializationLevel.ADVANCED:
        return 'Advanced';
      case SpecializationLevel.EXPERT:
        return 'Expert';
      case SpecializationLevel.MASTER:
        return 'Master';
      default:
        return 'Unknown';
    }
  }
}
