import {
  <PERSON><PERSON><PERSON>,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { StaffSalonAccess } from './StaffSalonAccess';
import { Permission } from './Permission';
import { User } from './User';

@Entity('staff_permissions')
@Index(['staffSalonAccessId'])
@Index(['permissionId'])
@Index(['grantedBy'])
@Index(['staffSalonAccessId', 'isActive'])
export class StaffPermission {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'uuid',
    comment: 'Reference to staff salon access record',
  })
  staffSalonAccessId!: string;

  @Column({
    type: 'uuid',
    comment: 'Reference to permission',
  })
  permissionId!: string;

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether this permission is currently active',
  })
  isActive!: boolean;

  @Column({
    type: 'uuid',
    comment: 'ID of the admin who granted this permission',
  })
  grantedBy!: string;

  @Column({
    type: 'timestamp',
    comment: 'When the permission was granted',
  })
  grantedAt!: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'ID of the admin who revoked this permission',
  })
  revokedBy?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When the permission was revoked',
  })
  revokedAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Notes about this permission assignment',
  })
  notes?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When this permission expires (null = never expires)',
  })
  expiresAt?: Date;

  // Relationships
  @ManyToOne(() => StaffSalonAccess, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'staffSalonAccessId' })
  staffSalonAccess!: StaffSalonAccess;

  @ManyToOne(() => Permission, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'permissionId' })
  permission!: Permission;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'grantedBy' })
  grantor!: User;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'revokedBy' })
  revoker?: User;

  @CreateDateColumn()
  createdAt!: Date;

  @UpdateDateColumn()
  updatedAt!: Date;

  // Virtual properties
  get isCurrentlyActive(): boolean {
    if (!this.isActive) return false;
    if (this.revokedAt) return false;
    if (this.expiresAt && this.expiresAt < new Date()) return false;
    return true;
  }

  get status(): 'ACTIVE' | 'REVOKED' | 'EXPIRED' | 'INACTIVE' {
    if (!this.isActive) return 'INACTIVE';
    if (this.revokedAt) return 'REVOKED';
    if (this.expiresAt && this.expiresAt < new Date()) return 'EXPIRED';
    return 'ACTIVE';
  }

  get displayStatus(): string {
    switch (this.status) {
      case 'ACTIVE':
        return 'Active';
      case 'REVOKED':
        return 'Revoked';
      case 'EXPIRED':
        return 'Expired';
      case 'INACTIVE':
        return 'Inactive';
      default:
        return 'Unknown';
    }
  }
}
