import { Router } from 'express';
import { body, param, query } from 'express-validator';
import { staffServiceAssignmentController } from '../controllers/staffServiceAssignmentController';
import { UserRole } from '../entities/User';
import { authorize, protect } from '../middlewares/authMiddleware';

const router = Router();

// Apply authentication to all routes
router.use(protect);

// Validation middleware
const createAssignmentValidation = [
  body('staffSalonAccessId')
    .isUUID()
    .withMessage('Valid staff salon access ID is required'),
  body('serviceId')
    .isUUID()
    .withMessage('Valid service ID is required'),
  body('specializationLevel')
    .optional()
    .isIn(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', 'MASTER'])
    .withMessage('Invalid specialization level'),
  body('commissionRate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Commission rate must be between 0 and 100'),
  body('customRate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Custom rate must be positive'),
  body('isBookable')
    .optional()
    .isBoolean()
    .withMessage('isBookable must be a boolean'),
  body('priority')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Priority must be a positive integer'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be less than 1000 characters'),
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date'),
];

const updateAssignmentValidation = [
  param('assignmentId')
    .isUUID()
    .withMessage('Valid assignment ID is required'),
  body('status')
    .optional()
    .isIn(['ACTIVE', 'INACTIVE', 'SUSPENDED'])
    .withMessage('Invalid status'),
  body('specializationLevel')
    .optional()
    .isIn(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', 'MASTER'])
    .withMessage('Invalid specialization level'),
  body('commissionRate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Commission rate must be between 0 and 100'),
  body('customRate')
    .optional()
    .isFloat({ min: 0 })
    .withMessage('Custom rate must be positive'),
  body('isBookable')
    .optional()
    .isBoolean()
    .withMessage('isBookable must be a boolean'),
  body('priority')
    .optional()
    .isInt({ min: 0 })
    .withMessage('Priority must be a positive integer'),
  body('notes')
    .optional()
    .isLength({ max: 1000 })
    .withMessage('Notes must be less than 1000 characters'),
  body('startDate')
    .optional()
    .isISO8601()
    .withMessage('Start date must be a valid date'),
  body('endDate')
    .optional()
    .isISO8601()
    .withMessage('End date must be a valid date'),
];

const bulkAssignValidation = [
  body('staffSalonAccessId')
    .isUUID()
    .withMessage('Valid staff salon access ID is required'),
  body('serviceIds')
    .isArray({ min: 1 })
    .withMessage('At least one service ID is required'),
  body('serviceIds.*')
    .isUUID()
    .withMessage('All service IDs must be valid UUIDs'),
  body('specializationLevel')
    .optional()
    .isIn(['BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', 'MASTER'])
    .withMessage('Invalid specialization level'),
  body('commissionRate')
    .optional()
    .isFloat({ min: 0, max: 100 })
    .withMessage('Commission rate must be between 0 and 100'),
  body('isBookable')
    .optional()
    .isBoolean()
    .withMessage('isBookable must be a boolean'),
];

const uuidParamValidation = [
  param('assignmentId')
    .isUUID()
    .withMessage('Valid assignment ID is required'),
];

const staffAccessParamValidation = [
  param('staffSalonAccessId')
    .isUUID()
    .withMessage('Valid staff salon access ID is required'),
];

const serviceParamValidation = [
  param('serviceId')
    .isUUID()
    .withMessage('Valid service ID is required'),
];

// Routes

/**
 * @route   POST /api/staff-service-assignments
 * @desc    Create a new staff-service assignment
 * @access  Private (ADMIN, PRODUCT_ADMIN)
 */
router.post(
  '/',
  authorize(UserRole.ADMIN, UserRole.PRODUCT_ADMIN),
  createAssignmentValidation,
  staffServiceAssignmentController.createAssignment
);

/**
 * @route   GET /api/staff-service-assignments
 * @desc    Get assignments with filters
 * @access  Private (ADMIN, PRODUCT_ADMIN, STAFF)
 */
router.get(
  '/',
  authorize(UserRole.ADMIN, UserRole.PRODUCT_ADMIN, UserRole.STAFF),
  staffServiceAssignmentController.getAssignments
);

/**
 * @route   GET /api/staff-service-assignments/:assignmentId
 * @desc    Get assignment by ID
 * @access  Private (ADMIN, PRODUCT_ADMIN, STAFF)
 */
router.get(
  '/:assignmentId',
  authorize(UserRole.ADMIN, UserRole.PRODUCT_ADMIN, UserRole.STAFF),
  uuidParamValidation,
  staffServiceAssignmentController.getAssignmentById
);

/**
 * @route   PUT /api/staff-service-assignments/:assignmentId
 * @desc    Update assignment
 * @access  Private (ADMIN, PRODUCT_ADMIN)
 */
router.put(
  '/:assignmentId',
  authorize(UserRole.ADMIN, UserRole.PRODUCT_ADMIN),
  updateAssignmentValidation,
  staffServiceAssignmentController.updateAssignment
);

/**
 * @route   DELETE /api/staff-service-assignments/:assignmentId
 * @desc    Delete assignment
 * @access  Private (ADMIN, PRODUCT_ADMIN)
 */
router.delete(
  '/:assignmentId',
  authorize(UserRole.ADMIN, UserRole.PRODUCT_ADMIN),
  uuidParamValidation,
  staffServiceAssignmentController.deleteAssignment
);

/**
 * @route   POST /api/staff-service-assignments/bulk-assign
 * @desc    Bulk assign services to staff
 * @access  Private (ADMIN, PRODUCT_ADMIN)
 */
router.post(
  '/bulk-assign',
  authorize(UserRole.ADMIN, UserRole.PRODUCT_ADMIN),
  bulkAssignValidation,
  staffServiceAssignmentController.bulkAssignServices
);

/**
 * @route   GET /api/staff-service-assignments/staff/:staffSalonAccessId/services
 * @desc    Get services assigned to staff
 * @access  Private (ADMIN, PRODUCT_ADMIN, STAFF)
 */
router.get(
  '/staff/:staffSalonAccessId/services',
  authorize(UserRole.ADMIN, UserRole.PRODUCT_ADMIN, UserRole.STAFF),
  staffAccessParamValidation,
  staffServiceAssignmentController.getStaffServices
);

/**
 * @route   GET /api/staff-service-assignments/service/:serviceId/staff
 * @desc    Get staff assigned to service
 * @access  Private (ADMIN, PRODUCT_ADMIN, STAFF)
 */
router.get(
  '/service/:serviceId/staff',
  authorize(UserRole.ADMIN, UserRole.PRODUCT_ADMIN, UserRole.STAFF),
  serviceParamValidation,
  staffServiceAssignmentController.getServiceStaff
);

export default router;
