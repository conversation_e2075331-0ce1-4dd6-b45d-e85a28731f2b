import { Router } from 'express';
import {
  getAvailability,
  getAvailabilityById,
  createAvailability,
  updateAvailability,
  deleteAvailability,
  approveAvailability,
  bulkCreateAvailability,
  blockTimeSlots,
  unblockTimeSlots,
} from '../controllers/staffAvailabilityController';

import { body, param, query } from 'express-validator';
import { protect } from '../middlewares/authMiddleware';

const router = Router();

// Apply authentication to all routes
router.use(protect);

/**
 * @swagger
 * /api/staff-availability:
 *   get:
 *     summary: Get staff availability records
 *     tags: [Staff Availability]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: query
 *         name: staffSalonAccessId
 *         schema:
 *           type: string
 *         description: Filter by staff salon access ID
 *       - in: query
 *         name: salonId
 *         schema:
 *           type: string
 *         description: Filter by salon ID
 *       - in: query
 *         name: type
 *         schema:
 *           type: string
 *           enum: [REGULAR, OVERRIDE, BREAK, UNAVAILABLE, SPECIAL]
 *         description: Filter by availability type
 *       - in: query
 *         name: status
 *         schema:
 *           type: string
 *           enum: [ACTIVE, INACTIVE, PENDING, REJECTED]
 *         description: Filter by status
 *       - in: query
 *         name: date
 *         schema:
 *           type: string
 *           format: date
 *         description: Filter by specific date
 *       - in: query
 *         name: dayOfWeek
 *         schema:
 *           type: string
 *         description: Filter by day of week
 *       - in: query
 *         name: page
 *         schema:
 *           type: integer
 *         description: Page number
 *       - in: query
 *         name: limit
 *         schema:
 *           type: integer
 *         description: Items per page
 *     responses:
 *       200:
 *         description: Staff availability records retrieved successfully
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get('/', getAvailability);

/**
 * @swagger
 * /api/staff-availability/{availabilityId}:
 *   get:
 *     summary: Get availability by ID
 *     tags: [Staff Availability]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: availabilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: Availability ID
 *     responses:
 *       200:
 *         description: Availability retrieved successfully
 *       404:
 *         description: Availability not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.get(
  '/:availabilityId',
  [
    param('availabilityId').isUUID().withMessage('Invalid availability ID'),
  ],
  getAvailabilityById
);

/**
 * @swagger
 * /api/staff-availability:
 *   post:
 *     summary: Create new availability record
 *     tags: [Staff Availability]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - staffSalonAccessId
 *               - type
 *               - timeSlots
 *               - isAvailable
 *             properties:
 *               staffSalonAccessId:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [REGULAR, OVERRIDE, BREAK, UNAVAILABLE, SPECIAL]
 *               date:
 *                 type: string
 *                 format: date
 *               dayOfWeek:
 *                 type: string
 *               timeSlots:
 *                 type: array
 *                 items:
 *                   type: object
 *               isAvailable:
 *                 type: boolean
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Availability created successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/',
  [
    body('staffSalonAccessId').isUUID().withMessage('Invalid staff salon access ID'),
    body('type').isIn(['REGULAR', 'OVERRIDE', 'BREAK', 'UNAVAILABLE', 'SPECIAL']).withMessage('Invalid availability type'),
    body('timeSlots').isArray().withMessage('Time slots must be an array'),
    body('isAvailable').isBoolean().withMessage('isAvailable must be a boolean'),
    body('date').optional().isISO8601().withMessage('Invalid date format'),
    body('dayOfWeek').optional().isString().withMessage('Day of week must be a string'),
    body('notes').optional().isString().withMessage('Notes must be a string'),
  ],
  createAvailability
);

/**
 * @swagger
 * /api/staff-availability/{availabilityId}:
 *   put:
 *     summary: Update availability record
 *     tags: [Staff Availability]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: availabilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: Availability ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             properties:
 *               type:
 *                 type: string
 *                 enum: [REGULAR, OVERRIDE, BREAK, UNAVAILABLE, SPECIAL]
 *               timeSlots:
 *                 type: array
 *                 items:
 *                   type: object
 *               isAvailable:
 *                 type: boolean
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Availability updated successfully
 *       400:
 *         description: Invalid request data
 *       404:
 *         description: Availability not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.put(
  '/:availabilityId',
  [
    param('availabilityId').isUUID().withMessage('Invalid availability ID'),
    body('type').optional().isIn(['REGULAR', 'OVERRIDE', 'BREAK', 'UNAVAILABLE', 'SPECIAL']).withMessage('Invalid availability type'),
    body('timeSlots').optional().isArray().withMessage('Time slots must be an array'),
    body('isAvailable').optional().isBoolean().withMessage('isAvailable must be a boolean'),
    body('notes').optional().isString().withMessage('Notes must be a string'),
  ],
  updateAvailability
);

/**
 * @swagger
 * /api/staff-availability/{availabilityId}:
 *   delete:
 *     summary: Delete availability record
 *     tags: [Staff Availability]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: availabilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: Availability ID
 *     responses:
 *       200:
 *         description: Availability deleted successfully
 *       404:
 *         description: Availability not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.delete(
  '/:availabilityId',
  [
    param('availabilityId').isUUID().withMessage('Invalid availability ID'),
  ],
  deleteAvailability
);

/**
 * @swagger
 * /api/staff-availability/{availabilityId}/approve:
 *   patch:
 *     summary: Approve or reject availability
 *     tags: [Staff Availability]
 *     security:
 *       - bearerAuth: []
 *     parameters:
 *       - in: path
 *         name: availabilityId
 *         required: true
 *         schema:
 *           type: string
 *         description: Availability ID
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - status
 *             properties:
 *               status:
 *                 type: string
 *                 enum: [ACTIVE, INACTIVE, PENDING, REJECTED]
 *               adminNotes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Availability approval updated successfully
 *       400:
 *         description: Invalid request data
 *       404:
 *         description: Availability not found
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.patch(
  '/:availabilityId/approve',
  [
    param('availabilityId').isUUID().withMessage('Invalid availability ID'),
    body('status').isIn(['ACTIVE', 'INACTIVE', 'PENDING', 'REJECTED']).withMessage('Invalid status'),
    body('adminNotes').optional().isString().withMessage('Admin notes must be a string'),
  ],
  approveAvailability
);

/**
 * @swagger
 * /api/staff-availability/bulk:
 *   post:
 *     summary: Bulk create availability records
 *     tags: [Staff Availability]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - staffSalonAccessId
 *               - type
 *               - daysOfWeek
 *               - timeSlots
 *               - isAvailable
 *             properties:
 *               staffSalonAccessId:
 *                 type: string
 *               type:
 *                 type: string
 *                 enum: [REGULAR, OVERRIDE, BREAK, UNAVAILABLE, SPECIAL]
 *               daysOfWeek:
 *                 type: array
 *                 items:
 *                   type: string
 *               timeSlots:
 *                 type: array
 *                 items:
 *                   type: object
 *               isAvailable:
 *                 type: boolean
 *               notes:
 *                 type: string
 *     responses:
 *       201:
 *         description: Availability records created successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/bulk',
  [
    body('staffSalonAccessId').isUUID().withMessage('Invalid staff salon access ID'),
    body('type').isIn(['REGULAR', 'OVERRIDE', 'BREAK', 'UNAVAILABLE', 'SPECIAL']).withMessage('Invalid availability type'),
    body('daysOfWeek').isArray().withMessage('Days of week must be an array'),
    body('timeSlots').isArray().withMessage('Time slots must be an array'),
    body('isAvailable').isBoolean().withMessage('isAvailable must be a boolean'),
    body('notes').optional().isString().withMessage('Notes must be a string'),
  ],
  bulkCreateAvailability
);

/**
 * @swagger
 * /api/staff-availability/block:
 *   post:
 *     summary: Block specific time slots for a date
 *     tags: [Staff Availability]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - staffSalonAccessId
 *               - date
 *               - blockedSlots
 *             properties:
 *               staffSalonAccessId:
 *                 type: string
 *               date:
 *                 type: string
 *                 format: date
 *               blockedSlots:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     startTime:
 *                       type: string
 *                     endTime:
 *                       type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Time slots blocked successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/block',
  [
    body('staffSalonAccessId').isUUID().withMessage('Invalid staff salon access ID'),
    body('date').isISO8601().withMessage('Invalid date format'),
    body('blockedSlots').isArray().withMessage('Blocked slots must be an array'),
    body('notes').optional().isString().withMessage('Notes must be a string'),
  ],
  blockTimeSlots
);

/**
 * @swagger
 * /api/staff-availability/unblock:
 *   post:
 *     summary: Unblock specific time slots for a date
 *     tags: [Staff Availability]
 *     security:
 *       - bearerAuth: []
 *     requestBody:
 *       required: true
 *       content:
 *         application/json:
 *           schema:
 *             type: object
 *             required:
 *               - staffSalonAccessId
 *               - date
 *               - unblockedSlots
 *             properties:
 *               staffSalonAccessId:
 *                 type: string
 *               date:
 *                 type: string
 *                 format: date
 *               unblockedSlots:
 *                 type: array
 *                 items:
 *                   type: object
 *                   properties:
 *                     startTime:
 *                       type: string
 *                     endTime:
 *                       type: string
 *               notes:
 *                 type: string
 *     responses:
 *       200:
 *         description: Time slots unblocked successfully
 *       400:
 *         description: Invalid request data
 *       401:
 *         description: Unauthorized
 *       500:
 *         description: Internal server error
 */
router.post(
  '/unblock',
  [
    body('staffSalonAccessId').isUUID().withMessage('Invalid staff salon access ID'),
    body('date').isISO8601().withMessage('Invalid date format'),
    body('unblockedSlots').isArray().withMessage('Unblocked slots must be an array'),
    body('notes').optional().isString().withMessage('Notes must be a string'),
  ],
  unblockTimeSlots
);

export default router;
