// Staff Availability Types (matching backend)
export enum AvailabilityType {
  REGULAR = 'REGULAR',           // Regular weekly schedule
  OVERRIDE = 'OVERRIDE',         // Override for specific date
  BREAK = 'BREAK',              // Break time
  UNAVAILABLE = 'UNAVAILABLE',   // Not available (sick, vacation, etc.)
  SPECIAL = 'SPECIAL',          // Special availability (extended hours, etc.)
}

export enum AvailabilityStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',          // Pending admin approval
  REJECTED = 'REJECTED',        // Rejected by admin
}

export interface TimeSlot {
  startTime: string;  // Format: "HH:mm" (24-hour)
  endTime: string;    // Format: "HH:mm" (24-hour)
  isAvailable: boolean;
  slotDuration?: number; // Duration in minutes (default: 30)
  maxBookings?: number;  // Max concurrent bookings (default: 1)
  notes?: string;
}

export interface StaffAvailability {
  id: string;
  staffSalonAccessId: string;
  type: AvailabilityType;
  status: AvailabilityStatus;
  date?: Date;
  dayOfWeek?: string;
  timeSlots: TimeSlot[];
  isAvailable: boolean;
  notes?: string;
  createdBy: string;
  createdAt: Date;
  approvedBy?: string;
  approvedAt?: Date;
  adminNotes?: string;
  lastModifiedBy?: string;
  lastModifiedAt?: Date;
  systemCreatedAt: Date;
  systemUpdatedAt: Date;

  // Relationships
  staffSalonAccess?: {
    id: string;
    staffId: string;
    salonId: string;
    staff: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      profileImage?: string;
    };
    salon: {
      id: string;
      name: string;
      logo?: string;
    };
  };
  creator?: {
    id: string;
    firstName: string;
    lastName: string;
  };
  approver?: {
    id: string;
    firstName: string;
    lastName: string;
  };

  // Virtual properties
  isRegularSchedule: boolean;
  isOverride: boolean;
  isPendingApproval: boolean;
  isApproved: boolean;
  totalAvailableHours: number;
  displayDate: string;
  displayStatus: string;
  displayType: string;
}

export interface CreateAvailabilityRequest {
  staffSalonAccessId: string;
  type: AvailabilityType;
  date?: Date;
  dayOfWeek?: string;
  timeSlots: TimeSlot[];
  isAvailable: boolean;
  notes?: string;
}

export interface UpdateAvailabilityRequest {
  type?: AvailabilityType;
  timeSlots?: TimeSlot[];
  isAvailable?: boolean;
  notes?: string;
}

export interface ApproveAvailabilityRequest {
  status: AvailabilityStatus;
  adminNotes?: string;
}

export interface AvailabilitySearchFilters {
  staffSalonAccessId?: string;
  salonId?: string;
  type?: AvailabilityType;
  status?: AvailabilityStatus;
  date?: Date;
  dateFrom?: Date;
  dateTo?: Date;
  dayOfWeek?: string;
  isAvailable?: boolean;
  pendingApproval?: boolean;
  page?: number;
  limit?: number;
}

export interface BulkCreateAvailabilityRequest {
  staffSalonAccessId: string;
  type: AvailabilityType;
  daysOfWeek: string[];
  timeSlots: TimeSlot[];
  isAvailable: boolean;
  notes?: string;
}

// Utility functions
export const getAvailabilityTypeLabel = (type: AvailabilityType): string => {
  const labels: Record<AvailabilityType, string> = {
    [AvailabilityType.REGULAR]: 'Regular Schedule',
    [AvailabilityType.OVERRIDE]: 'Schedule Override',
    [AvailabilityType.BREAK]: 'Break Time',
    [AvailabilityType.UNAVAILABLE]: 'Unavailable',
    [AvailabilityType.SPECIAL]: 'Special Hours',
  };
  return labels[type] || 'Unknown';
};

export const getAvailabilityTypeColor = (type: AvailabilityType): string => {
  const colors: Record<AvailabilityType, string> = {
    [AvailabilityType.REGULAR]: '#3B82F6', // Blue
    [AvailabilityType.OVERRIDE]: '#F59E0B', // Yellow
    [AvailabilityType.BREAK]: '#6B7280', // Gray
    [AvailabilityType.UNAVAILABLE]: '#EF4444', // Red
    [AvailabilityType.SPECIAL]: '#10B981', // Green
  };
  return colors[type] || '#6B7280';
};

export const getAvailabilityStatusLabel = (status: AvailabilityStatus): string => {
  const labels: Record<AvailabilityStatus, string> = {
    [AvailabilityStatus.ACTIVE]: 'Active',
    [AvailabilityStatus.INACTIVE]: 'Inactive',
    [AvailabilityStatus.PENDING]: 'Pending Approval',
    [AvailabilityStatus.REJECTED]: 'Rejected',
  };
  return labels[status] || 'Unknown';
};

export const getAvailabilityStatusColor = (status: AvailabilityStatus): string => {
  const colors: Record<AvailabilityStatus, string> = {
    [AvailabilityStatus.ACTIVE]: '#10B981', // Green
    [AvailabilityStatus.INACTIVE]: '#6B7280', // Gray
    [AvailabilityStatus.PENDING]: '#F59E0B', // Yellow
    [AvailabilityStatus.REJECTED]: '#EF4444', // Red
  };
  return colors[status] || '#6B7280';
};

export const DAYS_OF_WEEK = [
  'monday',
  'tuesday',
  'wednesday',
  'thursday',
  'friday',
  'saturday',
  'sunday',
] as const;

export const DAY_LABELS: Record<string, string> = {
  monday: 'Monday',
  tuesday: 'Tuesday',
  wednesday: 'Wednesday',
  thursday: 'Thursday',
  friday: 'Friday',
  saturday: 'Saturday',
  sunday: 'Sunday',
};

export const formatTime = (time: string): string => {
  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  return `${displayHour}:${minutes} ${ampm}`;
};

export const formatTimeSlot = (slot: TimeSlot): string => {
  return `${formatTime(slot.startTime)} - ${formatTime(slot.endTime)}`;
};

export const formatTimeSlots = (slots: TimeSlot[]): string => {
  if (!slots || slots.length === 0) return 'No time slots';
  
  const availableSlots = slots.filter(slot => slot.isAvailable);
  if (availableSlots.length === 0) return 'Not available';
  
  return availableSlots.map(formatTimeSlot).join(', ');
};

export const calculateTotalHours = (slots: TimeSlot[]): number => {
  if (!slots) return 0;
  
  return slots
    .filter(slot => slot.isAvailable)
    .reduce((total, slot) => {
      const start = new Date(`2000-01-01T${slot.startTime}:00`);
      const end = new Date(`2000-01-01T${slot.endTime}:00`);
      const hours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
      return total + hours;
    }, 0);
};

export const generateTimeSlots = (
  startTime: string,
  endTime: string,
  slotDuration: number = 30
): TimeSlot[] => {
  const slots: TimeSlot[] = [];
  const start = new Date(`2000-01-01T${startTime}:00`);
  const end = new Date(`2000-01-01T${endTime}:00`);
  
  let current = new Date(start);
  
  while (current < end) {
    const slotStart = current.toTimeString().slice(0, 5);
    current.setMinutes(current.getMinutes() + slotDuration);
    
    if (current <= end) {
      const slotEnd = current.toTimeString().slice(0, 5);
      
      slots.push({
        startTime: slotStart,
        endTime: slotEnd,
        isAvailable: true,
        slotDuration,
        maxBookings: 1,
      });
    }
  }
  
  return slots;
};

export const getDefaultTimeSlots = (): TimeSlot[] => {
  return generateTimeSlots('09:00', '17:00', 30);
};

export const validateTimeSlot = (slot: TimeSlot): string[] => {
  const errors: string[] = [];
  
  // Validate time format
  const timeRegex = /^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/;
  
  if (!timeRegex.test(slot.startTime)) {
    errors.push('Invalid start time format. Use HH:mm format');
  }
  
  if (!timeRegex.test(slot.endTime)) {
    errors.push('Invalid end time format. Use HH:mm format');
  }
  
  // Validate start time is before end time
  if (slot.startTime && slot.endTime) {
    const start = new Date(`2000-01-01T${slot.startTime}:00`);
    const end = new Date(`2000-01-01T${slot.endTime}:00`);
    
    if (start >= end) {
      errors.push('Start time must be before end time');
    }
  }
  
  // Validate slot duration
  if (slot.slotDuration && (slot.slotDuration < 15 || slot.slotDuration > 480)) {
    errors.push('Slot duration must be between 15 minutes and 8 hours');
  }
  
  // Validate max bookings
  if (slot.maxBookings && (slot.maxBookings < 1 || slot.maxBookings > 10)) {
    errors.push('Max bookings must be between 1 and 10');
  }
  
  return errors;
};

export const validateAvailabilityForm = (availability: Partial<CreateAvailabilityRequest>): string[] => {
  const errors: string[] = [];
  
  if (!availability.staffSalonAccessId) {
    errors.push('Staff member is required');
  }
  
  if (!availability.type) {
    errors.push('Availability type is required');
  }
  
  if (availability.type === AvailabilityType.REGULAR && !availability.dayOfWeek) {
    errors.push('Day of week is required for regular schedule');
  }
  
  if (availability.type === AvailabilityType.OVERRIDE && !availability.date) {
    errors.push('Date is required for schedule override');
  }
  
  if (!availability.timeSlots || availability.timeSlots.length === 0) {
    errors.push('At least one time slot is required');
  }
  
  if (availability.timeSlots) {
    availability.timeSlots.forEach((slot, index) => {
      const slotErrors = validateTimeSlot(slot);
      slotErrors.forEach(error => {
        errors.push(`Time slot ${index + 1}: ${error}`);
      });
    });
  }
  
  return errors;
};
