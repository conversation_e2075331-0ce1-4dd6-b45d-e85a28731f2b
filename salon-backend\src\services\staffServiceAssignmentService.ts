import { Repository } from 'typeorm';
import { AppDataSource } from '../config/typeorm';
import { StaffServiceAssignment, AssignmentStatus, SpecializationLevel } from '../entities/StaffServiceAssignment';
import { StaffSalonAccess } from '../entities/StaffSalonAccess';
import { Service } from '../entities/Service';
import { User } from '../entities/User';
import ApiError from '../utils/apiError';

export interface CreateStaffServiceAssignmentRequest {
  staffSalonAccessId: string;
  serviceId: string;
  specializationLevel?: SpecializationLevel;
  commissionRate?: number;
  customRate?: number;
  isBookable?: boolean;
  priority?: number;
  notes?: string;
  startDate?: Date;
  endDate?: Date;
  assignedBy: string;
}

export interface UpdateStaffServiceAssignmentRequest {
  status?: AssignmentStatus;
  specializationLevel?: SpecializationLevel;
  commissionRate?: number;
  customRate?: number;
  isBookable?: boolean;
  priority?: number;
  notes?: string;
  startDate?: Date;
  endDate?: Date;
  lastModifiedBy: string;
}

export interface BulkAssignServicesRequest {
  staffSalonAccessId: string;
  serviceIds: string[];
  specializationLevel?: SpecializationLevel;
  commissionRate?: number;
  isBookable?: boolean;
  assignedBy: string;
}

export interface AssignmentSearchFilters {
  staffSalonAccessId?: string;
  serviceId?: string;
  salonId?: string;
  status?: AssignmentStatus;
  specializationLevel?: SpecializationLevel;
  isBookable?: boolean;
  search?: string;
  sortBy?: 'service' | 'staff' | 'specialization' | 'priority' | 'createdAt';
  sortOrder?: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
}

class StaffServiceAssignmentService {
  private assignmentRepository: Repository<StaffServiceAssignment>;
  private staffAccessRepository: Repository<StaffSalonAccess>;
  private serviceRepository: Repository<Service>;
  private userRepository: Repository<User>;

  constructor() {
    this.assignmentRepository = AppDataSource.getRepository(StaffServiceAssignment);
    this.staffAccessRepository = AppDataSource.getRepository(StaffSalonAccess);
    this.serviceRepository = AppDataSource.getRepository(Service);
    this.userRepository = AppDataSource.getRepository(User);
  }

  /**
   * Create a new staff-service assignment
   */
  async createAssignment(data: CreateStaffServiceAssignmentRequest): Promise<StaffServiceAssignment> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Creating assignment');

      // Validate staff access exists
      const staffAccess = await this.staffAccessRepository.findOne({
        where: { id: data.staffSalonAccessId },
        relations: ['staff', 'salon']
      });

      if (!staffAccess) {
        throw new ApiError(404, 'Staff access not found');
      }

      // Validate service exists and belongs to the same salon
      const service = await this.serviceRepository.findOne({
        where: { id: data.serviceId, salonId: staffAccess.salonId, isActive: true }
      });

      if (!service) {
        throw new ApiError(404, 'Service not found or not available in this salon');
      }

      // Check if assignment already exists
      const existingAssignment = await this.assignmentRepository.findOne({
        where: {
          staffSalonAccessId: data.staffSalonAccessId,
          serviceId: data.serviceId
        }
      });

      if (existingAssignment) {
        throw new ApiError(400, 'Staff is already assigned to this service');
      }

      // Create assignment
      const assignment = this.assignmentRepository.create({
        ...data,
        startDate: data.startDate || new Date(),
        assignedAt: new Date(),
      });

      const savedAssignment = await this.assignmentRepository.save(assignment);

      console.log('✅ StaffServiceAssignmentService: Assignment created successfully');
      return savedAssignment;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error creating assignment:', error);
      throw error;
    }
  }

  /**
   * Get assignments with filters
   */
  async getAssignments(filters: AssignmentSearchFilters = {}): Promise<{
    data: StaffServiceAssignment[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Getting assignments with filters:', filters);

      const queryBuilder = this.assignmentRepository
        .createQueryBuilder('assignment')
        .leftJoinAndSelect('assignment.staffSalonAccess', 'staffAccess')
        .leftJoinAndSelect('staffAccess.staff', 'staff')
        .leftJoinAndSelect('assignment.service', 'service')
        .leftJoinAndSelect('assignment.assigner', 'assigner');

      // Apply filters
      if (filters.staffSalonAccessId) {
        queryBuilder.andWhere('assignment.staffSalonAccessId = :staffSalonAccessId', {
          staffSalonAccessId: filters.staffSalonAccessId
        });
      }

      if (filters.serviceId) {
        queryBuilder.andWhere('assignment.serviceId = :serviceId', {
          serviceId: filters.serviceId
        });
      }

      if (filters.salonId) {
        queryBuilder.andWhere('staffAccess.salonId = :salonId', {
          salonId: filters.salonId
        });
      }

      if (filters.status) {
        queryBuilder.andWhere('assignment.status = :status', {
          status: filters.status
        });
      }

      if (filters.specializationLevel) {
        queryBuilder.andWhere('assignment.specializationLevel = :specializationLevel', {
          specializationLevel: filters.specializationLevel
        });
      }

      if (filters.isBookable !== undefined) {
        queryBuilder.andWhere('assignment.isBookable = :isBookable', {
          isBookable: filters.isBookable
        });
      }

      if (filters.search) {
        queryBuilder.andWhere(
          '(LOWER(staff.firstName) LIKE LOWER(:search) OR LOWER(staff.lastName) LIKE LOWER(:search) OR LOWER(service.name) LIKE LOWER(:search))',
          { search: `%${filters.search}%` }
        );
      }

      // Apply sorting
      const sortBy = filters.sortBy || 'createdAt';
      const sortOrder = filters.sortOrder || 'DESC';

      switch (sortBy) {
        case 'service':
          queryBuilder.orderBy('service.name', sortOrder);
          break;
        case 'staff':
          queryBuilder.orderBy('staff.firstName', sortOrder);
          break;
        case 'specialization':
          queryBuilder.orderBy('assignment.specializationLevel', sortOrder);
          break;
        case 'priority':
          queryBuilder.orderBy('assignment.priority', sortOrder);
          break;
        default:
          queryBuilder.orderBy('assignment.createdAt', sortOrder);
      }

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const offset = (page - 1) * limit;

      const [assignments, total] = await queryBuilder
        .skip(offset)
        .take(limit)
        .getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      console.log('✅ StaffServiceAssignmentService: Retrieved assignments:', assignments.length);
      return {
        data: assignments,
        total,
        page,
        totalPages,
      };
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting assignments:', error);
      throw error;
    }
  }

  /**
   * Get assignment by ID
   */
  async getAssignmentById(assignmentId: string): Promise<StaffServiceAssignment> {
    try {
      const assignment = await this.assignmentRepository.findOne({
        where: { id: assignmentId },
        relations: ['staffSalonAccess', 'staffSalonAccess.staff', 'service', 'assigner']
      });

      if (!assignment) {
        throw new ApiError(404, 'Assignment not found');
      }

      return assignment;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting assignment:', error);
      throw error;
    }
  }

  /**
   * Update assignment
   */
  async updateAssignment(assignmentId: string, data: UpdateStaffServiceAssignmentRequest): Promise<StaffServiceAssignment> {
    try {
      const assignment = await this.getAssignmentById(assignmentId);

      Object.assign(assignment, {
        ...data,
        lastModifiedAt: new Date(),
      });

      const updatedAssignment = await this.assignmentRepository.save(assignment);

      console.log('✅ StaffServiceAssignmentService: Assignment updated successfully');
      return updatedAssignment;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error updating assignment:', error);
      throw error;
    }
  }

  /**
   * Delete assignment
   */
  async deleteAssignment(assignmentId: string): Promise<void> {
    try {
      const assignment = await this.getAssignmentById(assignmentId);
      await this.assignmentRepository.remove(assignment);

      console.log('✅ StaffServiceAssignmentService: Assignment deleted successfully');
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error deleting assignment:', error);
      throw error;
    }
  }

  /**
   * Bulk assign services to staff
   */
  async bulkAssignServices(data: BulkAssignServicesRequest): Promise<StaffServiceAssignment[]> {
    try {
      console.log('🔍 StaffServiceAssignmentService: Bulk assigning services');

      const assignments: StaffServiceAssignment[] = [];

      for (const serviceId of data.serviceIds) {
        try {
          const assignment = await this.createAssignment({
            staffSalonAccessId: data.staffSalonAccessId,
            serviceId,
            specializationLevel: data.specializationLevel,
            commissionRate: data.commissionRate,
            isBookable: data.isBookable,
            assignedBy: data.assignedBy,
          });
          assignments.push(assignment);
        } catch (error: any) {
          // Continue with other assignments if one fails
          console.warn('⚠️ Failed to assign service:', serviceId, error.message);
        }
      }

      console.log('✅ StaffServiceAssignmentService: Bulk assignment completed:', assignments.length);
      return assignments;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error in bulk assignment:', error);
      throw error;
    }
  }

  /**
   * Get services assigned to staff
   */
  async getStaffServices(staffSalonAccessId: string): Promise<StaffServiceAssignment[]> {
    try {
      const assignments = await this.assignmentRepository.find({
        where: {
          staffSalonAccessId,
          status: AssignmentStatus.ACTIVE
        },
        relations: ['service'],
        order: { priority: 'ASC', createdAt: 'DESC' }
      });

      return assignments;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting staff services:', error);
      throw error;
    }
  }

  /**
   * Get staff assigned to service
   */
  async getServiceStaff(serviceId: string): Promise<StaffServiceAssignment[]> {
    try {
      const assignments = await this.assignmentRepository.find({
        where: {
          serviceId,
          status: AssignmentStatus.ACTIVE
        },
        relations: ['staffSalonAccess', 'staffSalonAccess.staff'],
        order: { priority: 'ASC', createdAt: 'DESC' }
      });

      return assignments;
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentService: Error getting service staff:', error);
      throw error;
    }
  }
}

export const staffServiceAssignmentService = new StaffServiceAssignmentService();
