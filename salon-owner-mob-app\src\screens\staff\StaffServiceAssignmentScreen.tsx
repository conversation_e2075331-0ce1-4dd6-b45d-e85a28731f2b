import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { DarkTitle, DarkBody } from '../../components/common/Typography';
import { Button } from '../../components/common/Button';
import { Colors } from '../../constants/colors';
import { useToast } from '../../context/ToastContext';
import { useSalon } from '../../context/SalonContext';
import { staffServiceAssignmentService } from '../../services/staffServiceAssignmentService';
import { serviceService } from '../../services/serviceService';
import {
  StaffServiceAssignment,
  SpecializationLevel,
  getSpecializationLabel,
  getSpecializationColor,
  formatStaffName,
  formatServiceName,
} from '../../types/staffServiceAssignment';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width, height } = Dimensions.get('window');

type RootStackParamList = {
  StaffServiceAssignment: {
    staffId: string;
    staffName: string;
    staffSalonAccessId: string;
  };
};

type StaffServiceAssignmentScreenRouteProp = RouteProp<RootStackParamList, 'StaffServiceAssignment'>;
type StaffServiceAssignmentScreenNavigationProp = StackNavigationProp<RootStackParamList>;

interface Service {
  id: string;
  name: string;
  description?: string;
  category: string;
  duration: number;
  price?: number;
  priceMin?: number;
  priceMax?: number;
  image?: string;
  isActive: boolean;
}

export default function StaffServiceAssignmentScreen() {
  const route = useRoute<StaffServiceAssignmentScreenRouteProp>();
  const navigation = useNavigation<StaffServiceAssignmentScreenNavigationProp>();
  const { showToast } = useToast();
  const { selectedSalon } = useSalon();

  const { staffId, staffName, staffSalonAccessId } = route.params;

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [assignments, setAssignments] = useState<StaffServiceAssignment[]>([]);
  const [availableServices, setAvailableServices] = useState<Service[]>([]);
  const [selectedServices, setSelectedServices] = useState<string[]>([]);
  const [isAssigning, setIsAssigning] = useState(false);

  useEffect(() => {
    loadData();
  }, []);

  const loadData = async () => {
    try {
      setLoading(true);
      await Promise.all([
        loadAssignments(),
        loadAvailableServices(),
      ]);
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentScreen: Error loading data:', error);
      showToast('Failed to load data', 'error');
    } finally {
      setLoading(false);
    }
  };

  const loadAssignments = async () => {
    try {
      const staffAssignments = await staffServiceAssignmentService.getStaffServices(staffSalonAccessId);
      setAssignments(staffAssignments);
      console.log('✅ StaffServiceAssignmentScreen: Loaded assignments:', staffAssignments.length);
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentScreen: Error loading assignments:', error);
      throw error;
    }
  };

  const loadAvailableServices = async () => {
    try {
     
      if (!selectedSalon?.id) {
        throw new Error('No salon selected');
      }
 console.log('🔍 StaffServiceAssignmentScreen: Loading available selectedSalon',selectedSalon);
      const unassignedServices = await staffServiceAssignmentService.getUnassignedServices(
        staffSalonAccessId,
        selectedSalon.id
      );
      setAvailableServices(unassignedServices);
      console.log('✅ StaffServiceAssignmentScreen: Loaded available services:', unassignedServices);
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentScreen: Error loading available services:', error);
      throw error;
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleServiceToggle = (serviceId: string) => {
    setSelectedServices(prev => {
      if (prev.includes(serviceId)) {
        return prev.filter(id => id !== serviceId);
      } else {
        return [...prev, serviceId];
      }
    });
  };

  const handleBulkAssign = async () => {
    if (selectedServices.length === 0) {
      showToast('Please select at least one service', 'error');
      return;
    }

    try {
      setIsAssigning(true);

      await staffServiceAssignmentService.bulkAssignServices({
        staffSalonAccessId,
        serviceIds: selectedServices,
        specializationLevel: SpecializationLevel.INTERMEDIATE,
        isBookable: true,
      });

      showToast(`Successfully assigned ${selectedServices.length} service${selectedServices.length > 1 ? 's' : ''}`, 'success');
      setSelectedServices([]);
      await loadData();
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentScreen: Error assigning services:', error);
      showToast('Failed to assign services', 'error');
    } finally {
      setIsAssigning(false);
    }
  };

  const handleRemoveAssignment = async (assignment: StaffServiceAssignment) => {
    Alert.alert(
      'Remove Service Assignment',
      `Remove ${formatServiceName(assignment)} from ${staffName}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Remove',
          style: 'destructive',
          onPress: async () => {
            try {
              await staffServiceAssignmentService.deleteAssignment(assignment.id);
              showToast('Service assignment removed', 'success');
              await loadData();
            } catch (error: any) {
              console.error('❌ StaffServiceAssignmentScreen: Error removing assignment:', error);
              showToast('Failed to remove assignment', 'error');
            }
          },
        },
      ]
    );
  };

  const renderAssignedService = (assignment: StaffServiceAssignment) => (
    <View key={assignment.id} style={styles.serviceCard}>
      <View style={styles.serviceInfo}>
        <DarkTitle level={4} style={styles.serviceName}>
          {formatServiceName(assignment)}
        </DarkTitle>
        <DarkBody size="small" style={styles.serviceCategory}>
          {assignment.service?.category || 'Unknown Category'}
        </DarkBody>
        <View style={styles.serviceDetails}>
          <View style={[styles.specializationChip, { backgroundColor: getSpecializationColor(assignment.specializationLevel) + '20' }]}>
            <DarkBody size="small" style={[styles.specializationText, { color: getSpecializationColor(assignment.specializationLevel) }]}>
              {getSpecializationLabel(assignment.specializationLevel)}
            </DarkBody>
          </View>
          {assignment.service?.duration && (
            <DarkBody size="small" style={styles.serviceDuration}>
              {assignment.service.duration} min
            </DarkBody>
          )}
        </View>
      </View>
      <TouchableOpacity
        style={styles.removeButton}
        onPress={() => handleRemoveAssignment(assignment)}
      >
        <Icon name="remove-circle" size={24} color={Colors.error} />
      </TouchableOpacity>
    </View>
  );

  const renderAvailableService = (service: Service) => {
    const isSelected = selectedServices.includes(service.id);
    
    return (
      <TouchableOpacity
        key={service.id}
        style={[styles.serviceCard, isSelected && styles.selectedServiceCard]}
        onPress={() => handleServiceToggle(service.id)}
      >
        <View style={styles.serviceInfo}>
          <DarkTitle level={4} style={styles.serviceName}>
            {service.name}
          </DarkTitle>
          <DarkBody size="small" style={styles.serviceCategory}>
            {service.category}
          </DarkBody>
          <View style={styles.serviceDetails}>
            <DarkBody size="small" style={styles.serviceDuration}>
              {service.duration} min
            </DarkBody>
            {service.price && (
              <DarkBody size="small" style={styles.servicePrice}>
                ${service.price}
              </DarkBody>
            )}
          </View>
        </View>
        <View style={styles.selectionIndicator}>
          <Icon 
            name={isSelected ? "check-circle" : "radio-button-unchecked"} 
            size={24} 
            color={isSelected ? Colors.primary : Colors.gray400} 
          />
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
          </TouchableOpacity>
          <DarkTitle level={3} style={styles.headerTitle}>
            Assign Services
          </DarkTitle>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <DarkBody>Loading...</DarkBody>
        </View>
      </View>
    );
  }

  return (
     <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <DarkTitle level={3} style={styles.headerTitle}>
          Assign Services
        </DarkTitle>
        <View style={{ width: 24 }} />
      </View>

      {/* Staff Info */}
      <View style={styles.staffInfo}>
        <DarkTitle level={4}>{staffName}</DarkTitle>
        <DarkBody size="small" style={styles.staffSubtitle}>
          {assignments.length} service{assignments.length !== 1 ? 's' : ''} assigned
        </DarkBody>
      </View>

      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={handleRefresh} />
        }
      >
        {/* Assigned Services */}
        {assignments.length > 0 && (
          <View style={styles.section}>
            <DarkTitle level={4} style={styles.sectionTitle}>
              Assigned Services ({assignments.length})
            </DarkTitle>
            {assignments.map(renderAssignedService)}
          </View>
        )}

        {/* Available Services */}
        {availableServices.length > 0 && (
          <View style={styles.section}>
            <DarkTitle level={4} style={styles.sectionTitle}>
              Available Services ({availableServices.length})
            </DarkTitle>
            <DarkBody size="small" style={styles.sectionSubtitle}>
              Select services to assign to {staffName}
            </DarkBody>
            {availableServices.map(renderAvailableService)}
          </View>
        )}

        {availableServices.length === 0 && assignments.length > 0 && (
          <View style={styles.emptyState}>
            <Icon name="check-circle" size={48} color={Colors.success} />
            <DarkTitle level={4} style={styles.emptyTitle}>
              All Services Assigned
            </DarkTitle>
            <DarkBody style={styles.emptyMessage}>
              {staffName} is assigned to all available services in this salon.
            </DarkBody>
          </View>
        )}

        {availableServices.length === 0 && assignments.length === 0 && (
          <View style={styles.emptyState}>
            <Icon name="work-off" size={48} color={Colors.gray400} />
            <DarkTitle level={4} style={styles.emptyTitle}>
              No Services Available
            </DarkTitle>
            <DarkBody style={styles.emptyMessage}>
              There are no services available to assign in this salon.
            </DarkBody>
          </View>
        )}
      </ScrollView>

      {/* Assign Button */}
      {selectedServices.length > 0 && (
        <View style={styles.assignButtonContainer}>
          <Button
            title={`Assign ${selectedServices.length} Service${selectedServices.length > 1 ? 's' : ''}`}
            onPress={handleBulkAssign}
            loading={isAssigning}
            disabled={isAssigning}
          />
        </View>
      )}
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    marginHorizontal: width * 0.05,
  },
  staffInfo: {
    padding: width * 0.05,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  staffSubtitle: {
    color: Colors.gray600,
    marginTop: height * 0.005,
  },
  content: {
    flex: 1,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  section: {
    padding: width * 0.05,
  },
  sectionTitle: {
    marginBottom: height * 0.01,
  },
  sectionSubtitle: {
    color: Colors.gray600,
    marginBottom: height * 0.02,
  },
  serviceCard: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.white,
    padding: width * 0.04,
    marginVertical: height * 0.005,
    borderRadius: width * 0.03,
    borderWidth: 1,
    borderColor: Colors.gray200,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },
  selectedServiceCard: {
    borderColor: Colors.primary,
    backgroundColor: Colors.primary + '10',
  },
  serviceInfo: {
    flex: 1,
  },
  serviceName: {
    marginBottom: height * 0.005,
  },
  serviceCategory: {
    color: Colors.gray600,
    marginBottom: height * 0.01,
  },
  serviceDetails: {
    flexDirection: 'row',
    alignItems: 'center',
    flexWrap: 'wrap',
  },
  specializationChip: {
    paddingHorizontal: width * 0.02,
    paddingVertical: height * 0.005,
    borderRadius: width * 0.02,
    marginRight: width * 0.02,
  },
  specializationText: {
    fontSize: 12,
    fontWeight: '600',
  },
  serviceDuration: {
    color: Colors.gray600,
    marginRight: width * 0.02,
  },
  servicePrice: {
    color: Colors.primary,
    fontWeight: '600',
  },
  removeButton: {
    padding: width * 0.02,
  },
  selectionIndicator: {
    padding: width * 0.02,
  },
  assignButtonContainer: {
    padding: width * 0.05,
    backgroundColor: Colors.white,
    borderTopWidth: 1,
    borderTopColor: Colors.gray200,
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    padding: width * 0.1,
    marginTop: height * 0.05,
  },
  emptyTitle: {
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
    textAlign: 'center',
  },
  emptyMessage: {
    textAlign: 'center',
    color: Colors.gray600,
    lineHeight: 20,
  },
});
