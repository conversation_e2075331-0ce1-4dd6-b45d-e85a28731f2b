import { MigrationInterface, QueryRunner } from 'typeorm';

export class CreateStaffServiceAssignment1748700000000 implements MigrationInterface {
  name = 'CreateStaffServiceAssignment1748700000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Create staff_service_assignments table
    await queryRunner.query(`
      CREATE TABLE "staff_service_assignments" (
        "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
        "staffSalonAccessId" uuid NOT NULL,
        "serviceId" uuid NOT NULL,
        "status" character varying NOT NULL DEFAULT 'ACTIVE',
        "specializationLevel" character varying NOT NULL DEFAULT 'INTERMEDIATE',
        "commissionRate" numeric(5,2),
        "customRate" numeric(10,2),
        "isBookable" boolean NOT NULL DEFAULT true,
        "priority" integer,
        "notes" text,
        "startDate" TIMESTAMP NOT NULL,
        "endDate" TIMESTAMP,
        "assignedBy" uuid NOT NULL,
        "assignedAt" TIMESTAMP NOT NULL,
        "lastModifiedBy" uuid,
        "lastModifiedAt" TIMESTAMP,
        "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
        "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
        CONSTRAINT "PK_staff_service_assignments" PRIMARY KEY ("id")
      )
    `);

    // Add unique constraint for staff-service combination
    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      ADD CONSTRAINT "UQ_staff_service_assignment" 
      UNIQUE ("staffSalonAccessId", "serviceId")
    `);

    // Add indexes for better performance
    await queryRunner.query(`
      CREATE INDEX "IDX_staff_service_assignment_staff" 
      ON "staff_service_assignments" ("staffSalonAccessId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_staff_service_assignment_service" 
      ON "staff_service_assignments" ("serviceId")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_staff_service_assignment_status" 
      ON "staff_service_assignments" ("status")
    `);

    await queryRunner.query(`
      CREATE INDEX "IDX_staff_service_assignment_assigned_by" 
      ON "staff_service_assignments" ("assignedBy")
    `);

    // Add foreign key constraints
    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      ADD CONSTRAINT "FK_staff_service_assignment_staff_access" 
      FOREIGN KEY ("staffSalonAccessId") 
      REFERENCES "staff_salon_access"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      ADD CONSTRAINT "FK_staff_service_assignment_service" 
      FOREIGN KEY ("serviceId") 
      REFERENCES "services"("id") 
      ON DELETE CASCADE ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      ADD CONSTRAINT "FK_staff_service_assignment_assigned_by" 
      FOREIGN KEY ("assignedBy") 
      REFERENCES "users"("id") 
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      ADD CONSTRAINT "FK_staff_service_assignment_last_modified_by" 
      FOREIGN KEY ("lastModifiedBy") 
      REFERENCES "users"("id") 
      ON DELETE SET NULL ON UPDATE NO ACTION
    `);

    // Add check constraints for valid enum values
    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      ADD CONSTRAINT "CHK_staff_service_assignment_status" 
      CHECK ("status" IN ('ACTIVE', 'INACTIVE', 'SUSPENDED'))
    `);

    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      ADD CONSTRAINT "CHK_staff_service_assignment_specialization" 
      CHECK ("specializationLevel" IN ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', 'MASTER'))
    `);

    // Add check constraints for valid ranges
    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      ADD CONSTRAINT "CHK_staff_service_assignment_commission_rate" 
      CHECK ("commissionRate" IS NULL OR ("commissionRate" >= 0 AND "commissionRate" <= 100))
    `);

    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      ADD CONSTRAINT "CHK_staff_service_assignment_custom_rate" 
      CHECK ("customRate" IS NULL OR "customRate" >= 0)
    `);

    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      ADD CONSTRAINT "CHK_staff_service_assignment_priority" 
      CHECK ("priority" IS NULL OR "priority" >= 0)
    `);

    console.log('✅ Migration: Created staff_service_assignments table with all constraints and indexes');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop foreign key constraints first
    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      DROP CONSTRAINT "FK_staff_service_assignment_last_modified_by"
    `);

    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      DROP CONSTRAINT "FK_staff_service_assignment_assigned_by"
    `);

    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      DROP CONSTRAINT "FK_staff_service_assignment_service"
    `);

    await queryRunner.query(`
      ALTER TABLE "staff_service_assignments" 
      DROP CONSTRAINT "FK_staff_service_assignment_staff_access"
    `);

    // Drop indexes
    await queryRunner.query(`DROP INDEX "IDX_staff_service_assignment_assigned_by"`);
    await queryRunner.query(`DROP INDEX "IDX_staff_service_assignment_status"`);
    await queryRunner.query(`DROP INDEX "IDX_staff_service_assignment_service"`);
    await queryRunner.query(`DROP INDEX "IDX_staff_service_assignment_staff"`);

    // Drop table
    await queryRunner.query(`DROP TABLE "staff_service_assignments"`);

    console.log('✅ Migration: Dropped staff_service_assignments table and all related constraints');
  }
}
