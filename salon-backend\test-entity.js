const { AppDataSource } = require('./dist/config/typeorm');

async function testEntity() {
  try {
    console.log('🔍 Testing StaffServiceAssignment entity...');
    
    // Initialize the data source
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection initialized');
    }

    // Try to get the repository
    const StaffServiceAssignment = AppDataSource.getRepository('StaffServiceAssignment');
    console.log('✅ StaffServiceAssignment repository found');

    // Try a simple query
    const count = await StaffServiceAssignment.count();
    console.log('✅ Query successful. Current assignments count:', count);

    console.log('🎉 Entity test completed successfully!');

  } catch (error) {
    console.error('❌ Entity test failed:', error.message);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// Run the test
testEntity()
  .then(() => {
    console.log('✅ Test completed successfully');
    process.exit(0);
  })
  .catch((error) => {
    console.error('💥 Test failed:', error);
    process.exit(1);
  });
