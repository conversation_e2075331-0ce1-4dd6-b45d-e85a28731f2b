import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  TouchableOpacity,
  ScrollView,
  Dimensions,
  ActivityIndicator,
} from 'react-native';
import { RouteProp, useRoute, useNavigation, useFocusEffect } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';
import { SafeAreaView } from 'react-native-safe-area-context';

import { DarkTitle, DarkBody } from '../../components/common/Typography';
import { Colors } from '../../constants/colors';
import { useToast } from '../../context/ToastContext';
import {
  StaffAvailability,
  AvailabilityStatus,
  AvailabilityType,
} from '../../types/staffAvailability';
import { staffAvailabilityService } from '../../services/staffAvailabilityService';

const { width, height } = Dimensions.get('window');

type RootStackParamList = {
  StaffCalendar: {
    staffId: string;
    staffName: string;
    staffSalonAccessId: string;
  };
  StaffTimeline: {
    availability: StaffAvailability | null;
    staffName: string;
    selectedDate?: Date;
    staffSalonAccessId: string;
  };
  StaffTimelineEditor: {
    availability: StaffAvailability;
    staffName: string;
    selectedDate: Date;
  };
};

type StaffCalendarScreenRouteProp = RouteProp<RootStackParamList, 'StaffCalendar'>;
type StaffCalendarScreenNavigationProp = StackNavigationProp<RootStackParamList>;

interface CalendarDay {
  date: Date;
  day: number;
  isCurrentMonth: boolean;
  isToday: boolean;
  blockedPercentage: number; // Percentage of time that is blocked
  hasOverride: boolean;
  isLoading: boolean;
}

interface MonthData {
  year: number;
  month: number;
  monthName: string;
  days: CalendarDay[];
}

export default function StaffCalendarScreen() {
  const route = useRoute<StaffCalendarScreenRouteProp>();
  const navigation = useNavigation<StaffCalendarScreenNavigationProp>();
  const { showToast } = useToast();

  const { staffId, staffName, staffSalonAccessId } = route.params;

  const [currentMonth, setCurrentMonth] = useState(new Date());
  const [monthData, setMonthData] = useState<MonthData | null>(null);
  const [loading, setLoading] = useState(true);
  const [allStaffAvailability, setAllStaffAvailability] = useState<StaffAvailability[]>([]);
  const [regularSchedule, setRegularSchedule] = useState<StaffAvailability[]>([]);
  const [dataLoaded, setDataLoaded] = useState(false);

  useEffect(() => {
    // Load staff availability data only once when component mounts
    loadStaffData();
  }, []);

  useEffect(() => {
    generateCalendarData();
  }, [currentMonth]);

  useEffect(() => {
    if (dataLoaded) {
      updateCalendarWithAvailability();
    }
  }, [dataLoaded]);

  // Refresh calendar data when screen comes into focus (e.g., returning from timeline)
  useFocusEffect(
    React.useCallback(() => {
      if (dataLoaded) {
        console.log('🔄 StaffCalendarScreen: Screen focused, refreshing calendar data');
     refreshCalendarData()
      }
    }, [dataLoaded, staffSalonAccessId])
  );

  const refreshCalendarData = async () => {
    try {
      console.log('📡 StaffCalendarScreen: Refreshing availability data');

      // Reload ALL availability data (same as initial load)
      const [allOverrides, regularData] = await Promise.all([
        // Get ALL date-specific overrides (no date filtering)
        staffAvailabilityService.getAvailability({
          staffSalonAccessId,
          type: AvailabilityType.OVERRIDE,
          status: AvailabilityStatus.ACTIVE,
          limit: 1000, // Get a large number to cover all overrides
        }),
        // Get regular weekly schedule
        staffAvailabilityService.getAvailability({
          staffSalonAccessId,
          type: AvailabilityType.REGULAR,
          status: AvailabilityStatus.ACTIVE,
        })
      ]);

      console.log('✅ Refreshed data:', allOverrides.data.length, 'date-specific blocks and', regularData.data.length, 'regular schedules');

      // Update state with fresh data
      setAllStaffAvailability(allOverrides.data);
      setRegularSchedule(regularData.data);

      // Update calendar immediately with the fresh data (don't wait for state)
      updateCalendarWithFreshData(allOverrides.data, regularData.data);

    } catch (error: any) {
      console.error('❌ StaffCalendarScreen: Error refreshing data:', error);
      // Don't show error toast for refresh failures, just log it
    }
  };

  const generateCalendarData = () => {
    const year = currentMonth.getFullYear();
    const month = currentMonth.getMonth();
    
    // Get first day of month and calculate starting date
    const firstDay = new Date(year, month, 1);
    const startDate = new Date(firstDay);
    startDate.setDate(startDate.getDate() - firstDay.
    
    getDay()); // Start from Sunday
    
    // Generate 42 days (6 weeks)
    const days: CalendarDay[] = [];
    const today = new Date();
    
    for (let i = 0; i < 42; i++) {
      const date = new Date(startDate);
      date.setDate(startDate.getDate() + i);
      
      const isCurrentMonth = date.getMonth() === month;
      const isToday = date.toDateString() === today.toDateString();
      
      days.push({
        date: new Date(date),
        day: date.getDate(),
        isCurrentMonth,
        isToday,
        blockedPercentage: 0,
        hasOverride: false,
        isLoading: isCurrentMonth && !dataLoaded, // Only show loading if data not loaded
      });
    }

    const monthNames = [
      'January', 'February', 'March', 'April', 'May', 'June',
      'July', 'August', 'September', 'October', 'November', 'December'
    ];

    const newMonthData = {
      year,
      month,
      monthName: monthNames[month],
      days,
    };

    setMonthData(newMonthData);

    // If data is already loaded, immediately update the calendar
    if (dataLoaded) {
      // Use setTimeout to ensure state update completes first
      setTimeout(() => updateCalendarWithAvailability(), 0);
    }
  };

  // Load staff data only once when component mounts
  const loadStaffData = async () => {
    try {
      setLoading(true);

      console.log('🔍 StaffCalendarScreen: Loading ALL availability data for staff:', staffSalonAccessId);

      // Make ONLY 2 API calls total - get ALL data at once
      const [allOverrides, regularData] = await Promise.all([
        // Get ALL date-specific overrides (no date filtering)
        staffAvailabilityService.getAvailability({
          staffSalonAccessId,
          type: AvailabilityType.OVERRIDE,
          status: AvailabilityStatus.ACTIVE,
          limit: 1000, // Get a large number to cover all overrides
        }),
        // Get regular weekly schedule
        staffAvailabilityService.getAvailability({
          staffSalonAccessId,
          type: AvailabilityType.REGULAR,
          status: AvailabilityStatus.ACTIVE,
        })
      ]);

      console.log('✅ Loaded ALL data:', allOverrides.data.length, 'date-specific blocks and', regularData.data.length, 'regular schedules');

      // Store ALL data in state - no more API calls needed
      setAllStaffAvailability(allOverrides.data);
      setRegularSchedule(regularData.data);
      setDataLoaded(true);

    } catch (error: any) {
      console.error('❌ StaffCalendarScreen: Error loading staff data:', error);
      showToast('Failed to load staff availability data', 'error');
      setDataLoaded(true); // Still set to true to show fallback data
    } finally {
      setLoading(false);
    }
  };

  // Update calendar with fresh data (bypasses state delay)
  const updateCalendarWithFreshData = (freshAllStaffAvailability: StaffAvailability[], freshRegularSchedule: StaffAvailability[]) => {
    if (!monthData) {
      console.log('📅 Skipping calendar update - no month data');
      return;
    }

    console.log('📅 Updating calendar with FRESH data for', monthData.monthName, monthData.year);

    setMonthData(prev => {
      if (!prev) return prev;

      const updatedDays = prev.days.map(day => {
        if (!day.isCurrentMonth) {
          return { ...day, isLoading: false };
        }

        // Check for date-specific blocks using FRESH data
        const dateKey = day.date.toISOString().split('T')[0];
        const dateSpecificAvailability = freshAllStaffAvailability.find((avail: StaffAvailability) =>
          avail.date && new Date(avail.date).toISOString().split('T')[0] === dateKey
        );

        // Get regular schedule for this day of week using FRESH data
        const dayOfWeek = day.date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
        const regularAvail = freshRegularSchedule.find((avail: StaffAvailability) =>
          avail.dayOfWeek?.toLowerCase() === dayOfWeek
        );

        let blockedPercentage = 0;
        let hasOverride = false;

        if (dateSpecificAvailability) {
          // Staff has specific blocks for this date - calculate blocked percentage
          const blockedSlots = dateSpecificAvailability.timeSlots?.filter((slot: any) => !slot.isAvailable).length || 0;

          // Use standard working day (8 hours = 32 slots of 15 minutes each) as baseline
          const standardWorkingSlots = 32; // 8 hours × 4 slots per hour

          // Calculate percentage based on blocked slots vs standard working day
          blockedPercentage = Math.round((blockedSlots / standardWorkingSlots) * 100);
          hasOverride = true;

          console.log('📊 FRESH Date:', dateKey, 'Blocked slots:', blockedSlots, 'Standard slots:', standardWorkingSlots, 'Percentage:', blockedPercentage + '%');
        } else if (regularAvail) {
          // No date-specific blocks = 0% blocked (100% available)
          blockedPercentage = 0;
          hasOverride = false;
        } else {
          // No regular schedule for this day = not a working day = 0% blocked
          blockedPercentage = 0;
          hasOverride = false;
        }

        return {
          ...day,
          blockedPercentage: blockedPercentage,
          hasOverride: hasOverride,
          isLoading: false,
        };
      });

      return { ...prev, days: updatedDays };
    });
  };

  // Update calendar with availability data (called when month changes)
  const updateCalendarWithAvailability = () => {
    if (!monthData) {
      console.log('📅 Skipping calendar update - no month data');
      return;
    }

    if (!dataLoaded) {
      console.log('📅 Data not loaded yet, clearing loading states with default values');
      // Clear loading states even if data isn't loaded yet
      setMonthData(prev => {
        if (!prev) return prev;
        const updatedDays = prev.days.map(day => ({
          ...day,
          isLoading: false,
          blockedPercentage: day.isCurrentMonth ? 0 : day.blockedPercentage,
          hasOverride: false,
        }));
        return { ...prev, days: updatedDays };
      });
      return;
    }

    console.log('📅 Updating calendar for', monthData.monthName, monthData.year, '- using cached data');

    setMonthData(prev => {
      if (!prev) return prev;

      const updatedDays = prev.days.map(day => {
        if (!day.isCurrentMonth) {
          return { ...day, isLoading: false };
        }

        // Check for date-specific blocks in staff_availability table
        const dateKey = day.date.toISOString().split('T')[0];
        const dateSpecificAvailability = allStaffAvailability.find((avail: StaffAvailability) =>
          avail.date && new Date(avail.date).toISOString().split('T')[0] === dateKey
        );

        // Get regular schedule for this day of week
        const dayOfWeek = day.date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
        const regularAvail = regularSchedule.find((avail: StaffAvailability) =>
          avail.dayOfWeek?.toLowerCase() === dayOfWeek
        );


        let blockedPercentage = 0;
        let hasOverride = false;

        if (dateSpecificAvailability) {
          // Staff has specific blocks for this date - calculate blocked percentage
          const blockedSlots = dateSpecificAvailability.timeSlots?.filter((slot: any) => !slot.isAvailable).length || 0;

          // Use standard working day (8 hours = 32 slots of 15 minutes each) as baseline
          const standardWorkingSlots = 32; // 8 hours × 4 slots per hour

          // Calculate percentage based on blocked slots vs standard working day
          blockedPercentage = Math.round((blockedSlots / standardWorkingSlots) * 100);
          hasOverride = true;

          console.log('📊 Date:', dateKey, 'Blocked slots:', blockedSlots, 'Standard slots:', standardWorkingSlots, 'Percentage:', blockedPercentage + '%');
        } else if (regularAvail) {
          // No date-specific blocks in staff_availability table = 0% blocked (100% available)
          // This means staff is working this day but has no blocks/bookings
          blockedPercentage = 0;
          hasOverride = false;
        } else {
          // No regular schedule for this day = not a working day = 100% blocked
          blockedPercentage = 0;
          hasOverride = false;
        }

        return {
          ...day,
          blockedPercentage: blockedPercentage,
          hasOverride: hasOverride,
          isLoading: false,
        };
      });

      return { ...prev, days: updatedDays };
    });
  };

  const handleDatePress = (day: CalendarDay) => {
    if (!day.isCurrentMonth) return;

    try {
      const dateKey = day.date.toISOString().split('T')[0];

      // Check for date-specific override first (from cached data - NO API CALL)
      let availability = allStaffAvailability.find((avail: StaffAvailability) =>
        avail.date && new Date(avail.date).toISOString().split('T')[0] === dateKey
      );

      if (!availability) {
        // Use regular schedule for this day of week (from cached data - NO API CALL)
        const dayOfWeek = day.date.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
        availability = regularSchedule.find((avail: StaffAvailability) =>
          avail.dayOfWeek?.toLowerCase() === dayOfWeek
        );
      }

      // if (!availability) {
      //   showToast('Staff does not work on this day', 'warning');
      //   return;
      // }

      const hasDateSpecificBlocks = allStaffAvailability.find((avail: StaffAvailability) =>
        avail.date && new Date(avail.date).toISOString().split('T')[0] === dateKey
      );

      const availabilityType = hasDateSpecificBlocks
        ? `HAS BLOCKS in staff_availability table (${day.blockedPercentage}% blocked)`
        : `NO BLOCKS in staff_availability table (${day.blockedPercentage}% blocked = 100% available)`;

      console.log('📅 Opening timeline screen for', dateKey, 'with availability:', availabilityType, '(from cache)');

      // Navigate to timeline screen for this specific date - INSTANT!
      navigation.navigate('StaffTimeline', {
        availability: availability || null,
        staffName,
        selectedDate: day.date,
        staffSalonAccessId,
      });

    } catch (error: any) {
      console.error('❌ Error loading availability for date:', error);
      showToast('Failed to load availability for selected date', 'error');
    }
  };

  const navigateMonth = (direction: 'prev' | 'next') => {
    const newMonth = new Date(currentMonth);
    if (direction === 'prev') {
      newMonth.setMonth(newMonth.getMonth() - 1);
    } else {
      newMonth.setMonth(newMonth.getMonth() + 1);
    }
    setCurrentMonth(newMonth);
  };

  const getBlockedColor = (blockedPercentage: number): string => {
   
    // Color based on how much is blocked (inverse of availability)
    if (blockedPercentage === 0) return Colors.success; // 0% blocked = fully available (green)
    if (blockedPercentage <= 25) return Colors.warning; // 1-25% blocked = mostly available (yellow)
    if (blockedPercentage <= 50) return Colors.error; // 26-50% blocked = partially blocked (orange)
    if (blockedPercentage < 100) return Colors.error; // 51-99% blocked = heavily blocked (red)
    return Colors.gray400; // 100% blocked = not working day (gray)
  };

  const renderCalendarDay = (day: CalendarDay, index: number) => {
    const blockedColor = getBlockedColor(day.blockedPercentage);

    return (
      <TouchableOpacity
        key={index}
        style={[
          styles.dayCell,
          !day.isCurrentMonth && styles.otherMonthDay,
          day.isToday && styles.todayCell,
        ]}
        onPress={() => handleDatePress(day)}
        disabled={!day.isCurrentMonth}
        activeOpacity={0.7}
      >
        <DarkBody
          size="medium"
          style={[
            styles.dayNumber,
            !day.isCurrentMonth && styles.otherMonthText,
            day.isToday && styles.todayText,
          ]}
        >
          {day.day}
        </DarkBody>

        {day.isCurrentMonth && (
          <View style={styles.availabilityIndicator}>
            {day.isLoading ? (
              <ActivityIndicator size="small" color={Colors.primary} />
            ) : (
              <>
                <View
                  style={[
                    styles.availabilityBar,
                    { backgroundColor: blockedColor }
                  ]}
                />
                <DarkBody size="small" style={styles.percentageText}>
                  {day.blockedPercentage}% blocked
                </DarkBody>
                {day.hasOverride && (
                  <View style={styles.overrideIndicator}>
                    <Icon name="edit" size={8} color={Colors.primary} />
                  </View>
                )}
              </>
            )}
          </View>
        )}
      </TouchableOpacity>
    );
  };

  if (!monthData) {
    return (
      <SafeAreaView style={styles.container}>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <DarkBody style={styles.loadingText}>Loading calendar...</DarkBody>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <DarkTitle level={3} style={styles.headerTitle}>
          {staffName} Calendar
        </DarkTitle>
        <TouchableOpacity onPress={() => setCurrentMonth(new Date())}>
          <Icon name="today" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Month Navigation */}
      <View style={styles.monthNavigation}>
        <TouchableOpacity onPress={() => navigateMonth('prev')}>
          <Icon name="chevron-left" size={32} color={Colors.primary} />
        </TouchableOpacity>
        <DarkTitle level={2} style={styles.monthTitle}>
          {monthData.monthName} {monthData.year}
        </DarkTitle>
        <TouchableOpacity onPress={() => navigateMonth('next')}>
          <Icon name="chevron-right" size={32} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Calendar */}
      <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
        {/* Day Headers */}
        <View style={styles.dayHeaders}>
          {['Sun', 'Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat'].map((day, index) => (
            <View key={index} style={styles.dayHeader}>
              <DarkBody size="small" style={styles.dayHeaderText}>
                {day}
              </DarkBody>
            </View>
          ))}
        </View>

        {/* Calendar Grid */}
        <View style={styles.calendarGrid}>
          {monthData.days.map(renderCalendarDay)}
        </View>

        {/* Legend */}
        <View style={styles.legend}>
          <DarkTitle level={4} style={styles.legendTitle}>
            Staff Block Percentage Legend
          </DarkTitle>
          <View style={styles.legendItems}>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: Colors.success }]} />
              <DarkBody size="small">0% Blocked (Fully Available)</DarkBody>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: Colors.warning }]} />
              <DarkBody size="small">1-25% Blocked (Mostly Available)</DarkBody>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: Colors.error }]} />
              <DarkBody size="small">26-99% Blocked (Limited Availability)</DarkBody>
            </View>
            <View style={styles.legendItem}>
              <View style={[styles.legendColor, { backgroundColor: Colors.gray400 }]} />
              <DarkBody size="small">100% Blocked (Not Working Day)</DarkBody>
            </View>
          </View>
          <View style={styles.legendNote}>
            <Icon name="edit" size={16} color={Colors.primary} />
            <DarkBody size="small" style={styles.legendNoteText}>
              Edit icon indicates date-specific blocks from staff_availability table
            </DarkBody>
          </View>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    marginTop: height * 0.02,
    color: Colors.gray600,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    marginHorizontal: width * 0.05,
  },
  monthNavigation: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.025,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  monthTitle: {
    flex: 1,
    textAlign: 'center',
    color: Colors.textPrimary,
  },
  content: {
    flex: 1,
    backgroundColor: Colors.white,
  },
  dayHeaders: {
    flexDirection: 'row',
    paddingHorizontal: width * 0.02,
    paddingVertical: height * 0.015,
    backgroundColor: Colors.gray50,
  },
  dayHeader: {
    flex: 1,
    alignItems: 'center',
  },
  dayHeaderText: {
    color: Colors.gray600,
    fontWeight: '600',
  },
  calendarGrid: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    paddingHorizontal: width * 0.02,
  },
  dayCell: {
    width: (width - width * 0.04) / 7,
    height: height * 0.12,
    padding: width * 0.01,
    alignItems: 'center',
    justifyContent: 'space-between',
    borderWidth: 0.5,
    borderColor: Colors.gray200,
  },
  otherMonthDay: {
    backgroundColor: Colors.gray50,
  },
  todayCell: {
    backgroundColor: Colors.primary + '10',
    borderColor: Colors.primary,
    borderWidth: 2,
  },
  dayNumber: {
    fontWeight: '600',
    color: Colors.textPrimary,
  },
  otherMonthText: {
    color: Colors.gray400,
  },
  todayText: {
    color: Colors.primary,
    fontWeight: '700',
  },
  availabilityIndicator: {
    alignItems: 'center',
    justifyContent: 'center',
    width: '100%',
  },
  availabilityBar: {
    width: '80%',
    height: 4,
    borderRadius: 2,
    marginBottom: 2,
  },
  percentageText: {
    fontSize: 10,
    fontWeight: '600',
    color: Colors.gray700,
  },
  overrideIndicator: {
    position: 'absolute',
    top: -2,
    right: -2,
    backgroundColor: Colors.primary,
    borderRadius: 6,
    width: 12,
    height: 12,
    alignItems: 'center',
    justifyContent: 'center',
  },
  legend: {
    margin: width * 0.05,
    padding: width * 0.04,
    backgroundColor: Colors.gray50,
    borderRadius: width * 0.03,
    borderWidth: 1,
    borderColor: Colors.gray200,
  },
  legendTitle: {
    marginBottom: height * 0.015,
    color: Colors.textPrimary,
  },
  legendItems: {
    flexDirection: 'row',
    flexWrap: 'wrap',
    gap: width * 0.03,
  },
  legendItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: height * 0.01,
    minWidth: '45%',
  },
  legendColor: {
    width: 16,
    height: 4,
    borderRadius: 2,
    marginRight: width * 0.02,
  },
  legendNote: {
    flexDirection: 'row',
    alignItems: 'center',
    marginTop: height * 0.015,
    paddingTop: height * 0.015,
    borderTopWidth: 1,
    borderTopColor: Colors.gray300,
  },
  legendNoteText: {
    marginLeft: width * 0.02,
    color: Colors.gray600,
    fontStyle: 'italic',
  },
});
