import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Dimensions,
  ActivityIndicator,
  Image,
  Alert,
} from 'react-native';
import { useNavigation, useRoute } from '@react-navigation/native';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { ScreenWrapper } from '../../components/common/ScreenWrapper';
import { DarkTitle, DarkBody } from '../../components/common/Typography';
import { Colors } from '../../constants/colors';
import { staffService } from '../../services/staffService';
import { useToast } from '../../context/ToastContext';
import { useSalon } from '../../context/SalonContext';
import {
  StaffMember,
  formatStaffName,
  formatPosition,
  getStatusColor,
  getStatusLabel,
  getTodayWorkingHours,
} from '../../types/staff';

const { width, height } = Dimensions.get('window');

interface StaffDetailsScreenProps {
  route: {
    params: {
      staffId: string;
    };
  };
}

export const StaffDetailsScreen: React.FC<StaffDetailsScreenProps> = () => {
  const navigation = useNavigation();
  const route = useRoute();
  const { showToast } = useToast();
  const { selectedSalon } = useSalon();
  const { staffId } = route.params as { staffId: string };

  // State
  const [staff, setStaff] = useState<StaffMember | null>(null);
  const [loading, setLoading] = useState(true);

  // Load staff data
  useEffect(() => {
    loadStaffData();
  }, []);

  const loadStaffData = async () => {
    if (!selectedSalon || !staffId) return;

    try {
      setLoading(true);
      const staffData = await staffService.getStaffById(staffId, selectedSalon.id);
      setStaff(staffData);
      console.log('✅ StaffDetailsScreen: Staff data loaded staffData',staffData);
    } catch (error: any) {
      console.error('❌ StaffDetailsScreen: Error loading staff data:', error);
      showToast('Failed to load staff details', 'error');
      navigation.goBack();
    } finally {
      setLoading(false);
    }
  };

  // Navigation handlers
  const handleEdit = () => {
    if (staff) {
      navigation.navigate('EditStaff' as never, { staffId: staff.staffId } as never);
    }
  };

  const handleDelete = () => {
    if (!staff) return;

    Alert.alert(
      'Delete Staff Member',
      `Are you sure you want to remove ${formatStaffName(staff.staff)} from your salon? This action cannot be undone.`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: confirmDelete,
        },
      ]
    );
  };

  const confirmDelete = async () => {
    if (!selectedSalon || !staffId) return;

    try {
      await staffService.deleteStaff(staffId, selectedSalon.id);
      showToast('Staff member removed successfully', 'success');
      navigation.goBack();
    } catch (error: any) {
      console.error('❌ StaffDetailsScreen: Error deleting staff:', error);
      showToast('Failed to remove staff member', 'error');
    }
  };

  if (loading) {
    return (
      <ScreenWrapper>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
          </TouchableOpacity>
          <DarkTitle level={3} style={styles.headerTitle}>
            Staff Details
          </DarkTitle>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.loadingContainer}>
          <ActivityIndicator size="large" color={Colors.primary} />
          <DarkBody size="medium" style={styles.loadingText}>
            Loading staff details...
          </DarkBody>
        </View>
      </ScreenWrapper>
    );
  }

  if (!staff) {
    return (
      <ScreenWrapper>
        <View style={styles.header}>
          <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
          </TouchableOpacity>
          <DarkTitle level={3} style={styles.headerTitle}>
            Staff Details
          </DarkTitle>
          <View style={styles.headerActions} />
        </View>
        <View style={styles.errorContainer}>
          <Icon name="error" size={width * 0.15} color={Colors.error} />
          <DarkTitle level={3} textAlign="center" style={styles.errorTitle}>
            Staff Not Found
          </DarkTitle>
          <DarkBody size="medium" textAlign="center" style={styles.errorText}>
            The staff member you're looking for could not be found.
          </DarkBody>
        </View>
      </ScreenWrapper>
    );
  }

  return (
    <ScreenWrapper>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity style={styles.backButton} onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <DarkTitle level={3} style={styles.headerTitle}>
          Staff Details
        </DarkTitle>
        <View style={styles.headerActions}>
          <TouchableOpacity style={styles.actionButton} onPress={handleEdit}>
            <Icon name="edit" size={24} color={Colors.primary} />
          </TouchableOpacity>
          <TouchableOpacity style={styles.actionButton} onPress={handleDelete}>
            <Icon name="delete" size={24} color={Colors.error} />
          </TouchableOpacity>
        </View>
      </View>

      <ScrollView style={styles.container} showsVerticalScrollIndicator={false}>
        {/* Profile Section */}
        <View style={styles.section}>
          <View style={styles.profileHeader}>
            <View style={styles.avatarContainer}>
              {staff.staff.profileImage ? (
                <Image source={{ uri: staff.staff.profileImage }} style={styles.avatar} />
              ) : (
                <View style={styles.avatarPlaceholder}>
                  <DarkTitle level={2} style={styles.avatarText}>
                    {staff.staff.firstName.charAt(0)}{staff.staff.lastName.charAt(0)}
                  </DarkTitle>
                </View>
              )}
              <View style={[styles.statusBadge, { backgroundColor: getStatusColor(staff.status) }]} />
            </View>
            
            <View style={styles.profileInfo}>
              <DarkTitle level={2} style={styles.staffName}>
                {formatStaffName(staff.staff)}
              </DarkTitle>
              <DarkBody size="large" style={styles.staffPosition}>
                {formatPosition(staff.position)}
              </DarkBody>
              <View style={[styles.statusChip, { backgroundColor: getStatusColor(staff.status) }]}>
                <DarkBody size="small" style={styles.statusText}>
                  {getStatusLabel(staff.status)}
                </DarkBody>
              </View>
            </View>
          </View>
        </View>

        {/* Contact Information */}
        <View style={styles.section}>
          <DarkTitle level={4} style={styles.sectionTitle}>
            Contact Information
          </DarkTitle>
          
          <View style={styles.infoRow}>
            <Icon name="email" size={20} color={Colors.primary} />
            <DarkBody size="medium" style={styles.infoText}>
              {staff.staff.email}
            </DarkBody>
          </View>
          
          {staff.staff.phoneNumber && (
            <View style={styles.infoRow}>
              <Icon name="phone" size={20} color={Colors.primary} />
              <DarkBody size="medium" style={styles.infoText}>
                {staff.staff.phoneNumber}
              </DarkBody>
            </View>
          )}
        </View>

        {/* Job Information */}
        <View style={styles.section}>
          <DarkTitle level={4} style={styles.sectionTitle}>
            Job Information
          </DarkTitle>
          
          {staff.hourlyRate && (
            <View style={styles.infoRow}>
              <Icon name="attach-money" size={20} color={Colors.primary} />
              <DarkBody size="medium" style={styles.infoText}>
                ${staff.hourlyRate}/hour
              </DarkBody>
            </View>
          )}
          
          <View style={styles.infoRow}>
            <Icon name="schedule" size={20} color={Colors.primary} />
            <DarkBody size="medium" style={styles.infoText}>
              Today: {getTodayWorkingHours(staff.workingHours)}
            </DarkBody>
          </View>
          
          {staff.notes && (
            <View style={styles.notesContainer}>
              <DarkBody size="small" style={styles.notesLabel}>
                Notes:
              </DarkBody>
              <DarkBody size="medium" style={styles.notesText}>
                {staff.notes}
              </DarkBody>
            </View>
          )}
        </View>

        {/* Permissions */}
        <View style={styles.section}>
          <DarkTitle level={4} style={styles.sectionTitle}>
            Permissions ({staff.permissions?.length || 0})
          </DarkTitle>
          
          {staff.permissions && staff.permissions.length > 0 ? (
            staff.permissions.map((permission, index) => (
              <View key={index} style={styles.permissionItem}>
                <Icon name="check-circle" size={16} color={Colors.success} />
                <DarkBody size="medium" style={styles.permissionText}>
                  {permission.name}
                </DarkBody>
              </View>
            ))
          ) : (
            <DarkBody size="medium" style={styles.noPermissionsText}>
              No permissions assigned
            </DarkBody>
          )}
        </View>
      </ScrollView>
    </ScreenWrapper>
  );
};

const styles = StyleSheet.create({
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  backButton: {
    padding: width * 0.02,
  },
  headerTitle: {
    color: Colors.textPrimary,
    flex: 1,
    textAlign: 'center',
  },
  headerActions: {
    flexDirection: 'row',
  },
  actionButton: {
    padding: width * 0.02,
    marginLeft: width * 0.02,
  },
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  loadingText: {
    color: Colors.textSecondary,
    marginTop: height * 0.02,
  },
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: width * 0.1,
  },
  errorTitle: {
    color: Colors.textPrimary,
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
  },
  errorText: {
    color: Colors.textSecondary,
    textAlign: 'center',
  },
  section: {
    backgroundColor: Colors.white,
    marginHorizontal: width * 0.05,
    marginVertical: height * 0.01,
    borderRadius: width * 0.03,
    padding: width * 0.05,
  },
  sectionTitle: {
    color: Colors.textPrimary,
    marginBottom: height * 0.02,
  },
  profileHeader: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  avatarContainer: {
    position: 'relative',
    marginRight: width * 0.04,
  },
  avatar: {
    width: width * 0.2,
    height: width * 0.2,
    borderRadius: width * 0.1,
  },
  avatarPlaceholder: {
    width: width * 0.2,
    height: width * 0.2,
    borderRadius: width * 0.1,
    backgroundColor: Colors.primary,
    justifyContent: 'center',
    alignItems: 'center',
  },
  avatarText: {
    color: Colors.white,
    fontWeight: 'bold',
  },
  statusBadge: {
    position: 'absolute',
    bottom: width * 0.01,
    right: width * 0.01,
    width: width * 0.04,
    height: width * 0.04,
    borderRadius: width * 0.02,
    borderWidth: 2,
    borderColor: Colors.white,
  },
  profileInfo: {
    flex: 1,
  },
  staffName: {
    color: Colors.textPrimary,
    marginBottom: height * 0.005,
  },
  staffPosition: {
    color: Colors.primary,
    marginBottom: height * 0.01,
  },
  statusChip: {
    alignSelf: 'flex-start',
    paddingHorizontal: width * 0.03,
    paddingVertical: height * 0.005,
    borderRadius: width * 0.02,
  },
  statusText: {
    color: Colors.white,
    fontWeight: '600',
  },
  infoRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: height * 0.015,
  },
  infoText: {
    color: Colors.textPrimary,
    marginLeft: width * 0.03,
    flex: 1,
  },
  notesContainer: {
    marginTop: height * 0.01,
    paddingTop: height * 0.015,
    borderTopWidth: 1,
    borderTopColor: Colors.gray200,
  },
  notesLabel: {
    color: Colors.textSecondary,
    marginBottom: height * 0.005,
  },
  notesText: {
    color: Colors.textPrimary,
  },
  permissionItem: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: height * 0.01,
  },
  permissionText: {
    color: Colors.textPrimary,
    marginLeft: width * 0.02,
  },
  noPermissionsText: {
    color: Colors.textSecondary,
    fontStyle: 'italic',
  },
});
