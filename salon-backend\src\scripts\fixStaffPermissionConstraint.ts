import { AppDataSource } from '../config/typeorm';
import { FixStaffPermissionConstraint1748600000000 } from '../migrations/1748600000000-FixStaffPermissionConstraint';

/**
 * Fix the staff permission constraint issue
 * This script can be run independently to fix the database constraint
 */
export async function fixStaffPermissionConstraint(): Promise<void> {
  try {
    console.log('🔍 Fixing staff permission constraint...');
    
    // Initialize database connection if not already connected
    if (!AppDataSource.isInitialized) {
      await AppDataSource.initialize();
      console.log('✅ Database connection established');
    }

    // Run the migration
    const migration = new FixStaffPermissionConstraint1748600000000();
    const queryRunner = AppDataSource.createQueryRunner();
    
    await migration.up(queryRunner);
    
    await queryRunner.release();
    
    console.log('✅ Staff permission constraint fixed successfully');
    
  } catch (error) {
    console.error('❌ Error fixing staff permission constraint:', error);
    throw error;
  } finally {
    if (AppDataSource.isInitialized) {
      await AppDataSource.destroy();
    }
  }
}

// Run the script if called directly
if (require.main === module) {
  fixStaffPermissionConstraint()
    .then(() => {
      console.log('🎉 Script completed successfully');
      process.exit(0);
    })
    .catch((error) => {
      console.error('💥 Script failed:', error);
      process.exit(1);
    });
}
