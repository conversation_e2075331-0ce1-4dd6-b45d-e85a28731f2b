// Staff Service Assignment Types (matching backend)
export enum AssignmentStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  SUSPENDED = 'SUSPENDED',
}

export enum SpecializationLevel {
  BEGINNER = 'BEGINNER',
  INTERMEDIATE = 'INTERMEDIATE',
  ADVANCED = 'ADVANCED',
  EXPERT = 'EXPERT',
  MASTER = 'MASTER',
}

export interface StaffServiceAssignment {
  id: string;
  staffSalonAccessId: string;
  serviceId: string;
  status: AssignmentStatus;
  specializationLevel: SpecializationLevel;
  commissionRate?: number;
  customRate?: number;
  isBookable: boolean;
  priority?: number;
  notes?: string;
  startDate: Date;
  endDate?: Date;
  assignedBy: string;
  assignedAt: Date;
  lastModifiedBy?: string;
  lastModifiedAt?: Date;
  createdAt: Date;
  updatedAt: Date;

  // Relationships
  staffSalonAccess?: {
    id: string;
    staffId: string;
    salonId: string;
    staff: {
      id: string;
      firstName: string;
      lastName: string;
      email: string;
      profileImage?: string;
    };
  };
  service?: {
    id: string;
    name: string;
    description?: string;
    category: string;
    duration: number;
    price?: number;
    priceMin?: number;
    priceMax?: number;
    image?: string;
  };
  assigner?: {
    id: string;
    firstName: string;
    lastName: string;
  };

  // Virtual properties
  isCurrentlyActive: boolean;
  displayStatus: string;
  displaySpecialization: string;
}

export interface CreateStaffServiceAssignmentRequest {
  staffSalonAccessId: string;
  serviceId: string;
  specializationLevel?: SpecializationLevel;
  commissionRate?: number;
  customRate?: number;
  isBookable?: boolean;
  priority?: number;
  notes?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface UpdateStaffServiceAssignmentRequest {
  status?: AssignmentStatus;
  specializationLevel?: SpecializationLevel;
  commissionRate?: number;
  customRate?: number;
  isBookable?: boolean;
  priority?: number;
  notes?: string;
  startDate?: Date;
  endDate?: Date;
}

export interface BulkAssignServicesRequest {
  staffSalonAccessId: string;
  serviceIds: string[];
  specializationLevel?: SpecializationLevel;
  commissionRate?: number;
  isBookable?: boolean;
}

export interface AssignmentSearchFilters {
  staffSalonAccessId?: string;
  serviceId?: string;
  salonId?: string;
  status?: AssignmentStatus;
  specializationLevel?: SpecializationLevel;
  isBookable?: boolean;
  search?: string;
  sortBy?: 'service' | 'staff' | 'specialization' | 'priority' | 'createdAt';
  sortOrder?: 'ASC' | 'DESC';
  page?: number;
  limit?: number;
}

// Utility functions
export const getSpecializationLabel = (level: SpecializationLevel): string => {
  const labels: Record<SpecializationLevel, string> = {
    [SpecializationLevel.BEGINNER]: 'Beginner',
    [SpecializationLevel.INTERMEDIATE]: 'Intermediate',
    [SpecializationLevel.ADVANCED]: 'Advanced',
    [SpecializationLevel.EXPERT]: 'Expert',
    [SpecializationLevel.MASTER]: 'Master',
  };
  return labels[level] || 'Unknown';
};

export const getSpecializationColor = (level: SpecializationLevel): string => {
  const colors: Record<SpecializationLevel, string> = {
    [SpecializationLevel.BEGINNER]: '#6B7280', // Gray
    [SpecializationLevel.INTERMEDIATE]: '#3B82F6', // Blue
    [SpecializationLevel.ADVANCED]: '#10B981', // Green
    [SpecializationLevel.EXPERT]: '#F59E0B', // Yellow
    [SpecializationLevel.MASTER]: '#8B5CF6', // Purple
  };
  return colors[level] || '#6B7280';
};

export const getAssignmentStatusLabel = (status: AssignmentStatus): string => {
  const labels: Record<AssignmentStatus, string> = {
    [AssignmentStatus.ACTIVE]: 'Active',
    [AssignmentStatus.INACTIVE]: 'Inactive',
    [AssignmentStatus.SUSPENDED]: 'Suspended',
  };
  return labels[status] || 'Unknown';
};

export const getAssignmentStatusColor = (status: AssignmentStatus): string => {
  const colors: Record<AssignmentStatus, string> = {
    [AssignmentStatus.ACTIVE]: '#10B981', // Green
    [AssignmentStatus.INACTIVE]: '#6B7280', // Gray
    [AssignmentStatus.SUSPENDED]: '#F59E0B', // Yellow
  };
  return colors[status] || '#6B7280';
};

export const getSpecializationOptions = (): Array<{ label: string; value: SpecializationLevel; color: string }> => {
  return [
    { label: 'Beginner', value: SpecializationLevel.BEGINNER, color: getSpecializationColor(SpecializationLevel.BEGINNER) },
    { label: 'Intermediate', value: SpecializationLevel.INTERMEDIATE, color: getSpecializationColor(SpecializationLevel.INTERMEDIATE) },
    { label: 'Advanced', value: SpecializationLevel.ADVANCED, color: getSpecializationColor(SpecializationLevel.ADVANCED) },
    { label: 'Expert', value: SpecializationLevel.EXPERT, color: getSpecializationColor(SpecializationLevel.EXPERT) },
    { label: 'Master', value: SpecializationLevel.MASTER, color: getSpecializationColor(SpecializationLevel.MASTER) },
  ];
};

export const formatStaffName = (assignment: StaffServiceAssignment): string => {
  if (!assignment.staffSalonAccess?.staff) return 'Unknown Staff';
  const { firstName, lastName } = assignment.staffSalonAccess.staff;
  return `${firstName} ${lastName}`;
};

export const formatServiceName = (assignment: StaffServiceAssignment): string => {
  return assignment.service?.name || 'Unknown Service';
};

export const formatCommissionRate = (rate?: number): string => {
  return rate ? `${rate.toFixed(1)}%` : 'Not set';
};

export const formatCustomRate = (rate?: number): string => {
  return rate ? `$${rate.toFixed(2)}/hr` : 'Not set';
};

export const validateAssignmentForm = (assignment: Partial<CreateStaffServiceAssignmentRequest>): string[] => {
  const errors: string[] = [];

  if (!assignment.staffSalonAccessId) {
    errors.push('Staff member is required');
  }

  if (!assignment.serviceId) {
    errors.push('Service is required');
  }

  if (assignment.commissionRate && (assignment.commissionRate < 0 || assignment.commissionRate > 100)) {
    errors.push('Commission rate must be between 0 and 100');
  }

  if (assignment.customRate && assignment.customRate < 0) {
    errors.push('Custom rate cannot be negative');
  }

  if (assignment.priority && assignment.priority < 0) {
    errors.push('Priority cannot be negative');
  }

  return errors;
};
