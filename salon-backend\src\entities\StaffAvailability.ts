import {
  Entity,
  PrimaryGeneratedColumn,
  Column,
  CreateDateColumn,
  UpdateDateColumn,
  ManyToOne,
  JoinColumn,
  Index,
  Unique,
} from 'typeorm';
import { StaffSalonAccess } from './StaffSalonAccess';
import { User } from './User';

export enum AvailabilityType {
  REGULAR = 'REGULAR',           // Regular weekly schedule
  OVERRIDE = 'OVERRIDE',         // Override for specific date
  BREAK = 'BREAK',              // Break time
  UNAVAILABLE = 'UNAVAILABLE',   // Not available (sick, vacation, etc.)
  SPECIAL = 'SPECIAL', 
  DATE_SPECIFIC="DATE_SPECIFIC"         // Special availability (extended hours, etc.)
}

export enum AvailabilityStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  PENDING = 'PENDING',          // Pending admin approval
  REJECTED = 'REJECTED',        // Rejected by admin
}

export interface TimeSlot {
  startTime: string;  // Format: "HH:mm" (24-hour)
  endTime: string;    // Format: "HH:mm" (24-hour)
  isAvailable: boolean;
  slotDuration?: number; // Duration in minutes (default: 30)
  maxBookings?: number;  // Max concurrent bookings (default: 1)
  notes?: string;
}

@Entity('staff_availability')
@Unique(['staffSalonAccessId', 'date', 'type'])
@Index(['staffSalonAccessId'])
@Index(['date'])
@Index(['type'])
@Index(['status'])
@Index(['dayOfWeek'])
export class StaffAvailability {
  @PrimaryGeneratedColumn('uuid')
  id!: string;

  @Column({
    type: 'uuid',
    comment: 'Reference to staff salon access record',
  })
  staffSalonAccessId!: string;

  @Column({
    type: 'enum',
    enum: AvailabilityType,
    default: AvailabilityType.REGULAR,
    comment: 'Type of availability record',
  })
  type!: AvailabilityType;

  @Column({
    type: 'enum',
    enum: AvailabilityStatus,
    default: AvailabilityStatus.ACTIVE,
    comment: 'Status of availability record',
  })
  status!: AvailabilityStatus;

  @Column({
    type: 'date',
    nullable: true,
    comment: 'Specific date for override/special availability',
  })
  date?: Date;

  @Column({
    type: 'varchar',
    length: 10,
    nullable: true,
    comment: 'Day of week for regular schedule (monday, tuesday, etc.)',
  })
  dayOfWeek?: string;

  @Column({
    type: 'json',
    comment: 'Array of time slots for this availability',
  })
  timeSlots!: TimeSlot[];

  @Column({
    type: 'boolean',
    default: true,
    comment: 'Whether staff is available on this day/date',
  })
  isAvailable!: boolean;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Notes about this availability',
  })
  notes?: string;

  @Column({
    type: 'uuid',
    comment: 'ID of the user who created this record',
  })
  createdBy!: string;

  @Column({
    type: 'timestamp',
    comment: 'When this availability was created',
  })
  createdAt!: Date;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'ID of the admin who approved/rejected this record',
  })
  approvedBy?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When this availability was approved/rejected',
  })
  approvedAt?: Date;

  @Column({
    type: 'text',
    nullable: true,
    comment: 'Admin notes about approval/rejection',
  })
  adminNotes?: string;

  @Column({
    type: 'uuid',
    nullable: true,
    comment: 'ID of the user who last modified this record',
  })
  lastModifiedBy?: string;

  @Column({
    type: 'timestamp',
    nullable: true,
    comment: 'When this availability was last modified',
  })
  lastModifiedAt?: Date;

  // Relationships
  @ManyToOne(() => StaffSalonAccess, { onDelete: 'CASCADE' })
  @JoinColumn({ name: 'staffSalonAccessId' })
  staffSalonAccess!: StaffSalonAccess;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'createdBy' })
  creator!: User;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'approvedBy' })
  approver?: User;

  @ManyToOne(() => User, { onDelete: 'SET NULL' })
  @JoinColumn({ name: 'lastModifiedBy' })
  lastModifier?: User;

  @CreateDateColumn()
  systemCreatedAt!: Date;

  @UpdateDateColumn()
  systemUpdatedAt!: Date;

  // Virtual properties
  get isRegularSchedule(): boolean {
    return this.type === AvailabilityType.REGULAR;
  }

  get isOverride(): boolean {
    return this.type === AvailabilityType.OVERRIDE;
  }

  get isPendingApproval(): boolean {
    return this.status === AvailabilityStatus.PENDING;
  }

  get isApproved(): boolean {
    return this.status === AvailabilityStatus.ACTIVE;
  }

  get totalAvailableHours(): number {
    if (!this.isAvailable || !this.timeSlots) return 0;

    return this.timeSlots
      .filter(slot => slot.isAvailable)
      .reduce((total, slot) => {
        const start = new Date(`2000-01-01T${slot.startTime}:00`);
        const end = new Date(`2000-01-01T${slot.endTime}:00`);
        const hours = (end.getTime() - start.getTime()) / (1000 * 60 * 60);
        return total + hours;
      }, 0);
  }

  get blockedPercentage(): number {
    if (!this.timeSlots || this.timeSlots.length === 0) return 0;

    const totalSlots = this.timeSlots.length;
    const blockedSlots = this.timeSlots.filter(slot => !slot.isAvailable).length;

    return Math.round((blockedSlots / totalSlots) * 100);
  }

  get availablePercentage(): number {
    return 100 - this.blockedPercentage;
  }

  get displayDate(): string {
    if (this.date) {
      return this.date.toLocaleDateString();
    }
    if (this.dayOfWeek) {
      return this.dayOfWeek.charAt(0).toUpperCase() + this.dayOfWeek.slice(1);
    }
    return 'Unknown';
  }

  get displayStatus(): string {
    switch (this.status) {
      case AvailabilityStatus.ACTIVE:
        return 'Active';
      case AvailabilityStatus.INACTIVE:
        return 'Inactive';
      case AvailabilityStatus.PENDING:
        return 'Pending Approval';
      case AvailabilityStatus.REJECTED:
        return 'Rejected';
      default:
        return 'Unknown';
    }
  }

  get displayType(): string {
    switch (this.type) {
      case AvailabilityType.REGULAR:
        return 'Regular Schedule';
      case AvailabilityType.OVERRIDE:
        return 'Schedule Override';
      case AvailabilityType.BREAK:
        return 'Break Time';
      case AvailabilityType.UNAVAILABLE:
        return 'Unavailable';
      case AvailabilityType.SPECIAL:
        return 'Special Hours';
      default:
        return 'Unknown';
    }
  }
}
