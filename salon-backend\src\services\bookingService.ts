import { Repository, SelectQueryBuilder, Between, In } from 'typeorm';
import { AppDataSource } from '../config/typeorm';
import { Booking, BookingStatus, PaymentStatus } from '../entities/Booking';
import { Salon } from '../entities/Salon';
import { User } from '../entities/User';
import { Service } from '../entities/Service';
import { StaffSalonAccess } from '../entities/StaffSalonAccess';
import { StaffServiceAssignment, AssignmentStatus } from '../entities/StaffServiceAssignment';
import { StaffAvailability, AvailabilityType, AvailabilityStatus } from '../entities/StaffAvailability';
import {
  CreateBookingRequest,
  UpdateBookingRequest,
  BookingFilters,
  BookingResponse,
  BookingListResponse,
  AvailabilityRequest,
  AvailabilityResponse,
  AvailableStaff,
  BookingStats,
  BookingValidationResult,
  TimeSlot,
} from '../types/booking';
import ApiError from '../utils/apiError';

class BookingService {
  private get bookingRepository(): Repository<Booking> {
    return AppDataSource.getRepository(Booking);
  }

  private get salonRepository(): Repository<Salon> {
    return AppDataSource.getRepository(Salon);
  }

  private get userRepository(): Repository<User> {
    return AppDataSource.getRepository(User);
  }

  private get serviceRepository(): Repository<Service> {
    return AppDataSource.getRepository(Service);
  }

  private get staffAccessRepository(): Repository<StaffSalonAccess> {
    return AppDataSource.getRepository(StaffSalonAccess);
  }

  private get staffAssignmentRepository(): Repository<StaffServiceAssignment> {
    return AppDataSource.getRepository(StaffServiceAssignment);
  }

  private get staffAvailabilityRepository(): Repository<StaffAvailability> {
    return AppDataSource.getRepository(StaffAvailability);
  }

  /**
   * Get availability for a service on a specific date
   */
  async getAvailability(request: AvailabilityRequest): Promise<AvailabilityResponse> {
    try {
      console.log('🔍 BookingService: Getting availability for:', request);

      // Validate salon and service
      const salon = await this.salonRepository.findOne({
        where: { id: request.salonId, isActive: true }
      });

      if (!salon) {
        throw new ApiError(404, 'Salon not found');
      }

      const service = await this.serviceRepository.findOne({
        where: { id: request.serviceId, salonId: request.salonId, isActive: true }
      });

      if (!service) {
        throw new ApiError(404, 'Service not found');
      }

      // Get staff assigned to this service
      const currentDate = new Date();
      const staffAssignments = await this.staffAssignmentRepository
        .createQueryBuilder('assignment')
        .leftJoinAndSelect('assignment.staffSalonAccess', 'staffSalonAccess')
        .leftJoinAndSelect('staffSalonAccess.staff', 'staff')
        .where('assignment.serviceId = :serviceId', { serviceId: request.serviceId })
        .andWhere('assignment.status = :status', { status: AssignmentStatus.ACTIVE })
        .andWhere('assignment.isBookable = :isBookable', { isBookable: true })
        .andWhere('assignment.startDate <= :currentDate', { currentDate })
        .andWhere('(assignment.endDate IS NULL OR assignment.endDate > :currentDate)', { currentDate })
        .getMany();

      if (staffAssignments.length === 0) {
        return {
          date: request.date,
          availableStaff: [],
          totalSlots: 0,
          availableSlots: 0,
          busySlots: 0,
        };
      }

      const availableStaff: AvailableStaff[] = [];
      let totalSlots = 0;
      let availableSlots = 0;

      // Check availability for each staff member
      for (const assignment of staffAssignments) {
        const staff = assignment.staffSalonAccess;
        const staffAvailability = await this.getStaffAvailabilityForDate(
          staff.id,
          request.date
        );

        if (staffAvailability && staffAvailability.length > 0) {
          const slots = await this.generateTimeSlots(
            staffAvailability,
            service.duration,
            request.date,
            staff.id
          );

          if (slots.length > 0) {
            availableStaff.push({
              staffSalonAccessId: staff.id,
              staffId: staff.staffId,
              firstName: staff.staff.firstName,
              lastName: staff.staff.lastName,
              profileImage: staff.staff.profileImage,
              position: staff.position || 'Staff Member',
              specializationLevel: assignment.specializationLevel.toString() as "BEGINNER" | "INTERMEDIATE" | "ADVANCED" | "EXPERT",
              availableSlots: slots,
            });

            totalSlots += slots.length;
            availableSlots += slots.filter(slot => slot.isAvailable).length;
          }
        }
      }

      return {
        date: request.date,
        availableStaff,
        totalSlots,
        availableSlots,
        busySlots: totalSlots - availableSlots,
      };
    } catch (error: any) {
      console.error('❌ BookingService: Error getting availability:', error);
      throw error;
    }
  }

  /**
   * Create a new booking
   */
  async createBooking(data: CreateBookingRequest): Promise<BookingResponse> {
    try {
      console.log('🔍 BookingService: Creating booking:', data);

      // Validate the booking
      const validation = await this.validateBooking(data);
      if (!validation.isValid) {
        throw new ApiError(400, `Booking validation failed: ${validation.errors.join(', ')}`);
      }

      // Get service details for pricing
      const service = await this.serviceRepository.findOne({
        where: { id: data.serviceId }
      });

      if (!service) {
        throw new ApiError(404, 'Service not found');
      }

      // Calculate pricing (for now, use service price)
      const originalPrice = service.price ?? 0;
      const discountAmount = 0; // TODO: Apply offer discounts
      const finalPrice = originalPrice - discountAmount;

      // Calculate duration
      const startTime = new Date(`2000-01-01T${data.startTime}:00`);
      const endTime = new Date(`2000-01-01T${data.endTime}:00`);
      const duration = (endTime.getTime() - startTime.getTime()) / (1000 * 60); // minutes

      // Create booking
      const booking = this.bookingRepository.create({
        salonId: data.salonId,
        customerId: data.customerId,
        serviceId: data.serviceId,
        staffSalonAccessId: data.staffSalonAccessId,
        bookingDate: new Date(data.bookingDate),
        startTime: data.startTime,
        endTime: data.endTime,
        duration,
        originalPrice,
        discountAmount,
        finalPrice,
        notes: data.notes,
        specialRequests: data.specialRequests,
        status: BookingStatus.CONFIRMED, // Auto-confirm for now
        paymentStatus: PaymentStatus.PENDING,
        confirmedAt: new Date(),
      });

      const savedBooking = await this.bookingRepository.save(booking);

      console.log('✅ BookingService: Booking created successfully:', savedBooking.bookingNumber);
      
      // Return formatted response
      return await this.getBookingById(savedBooking.id);
    } catch (error: any) {
      console.error('❌ BookingService: Error creating booking:', error);
      throw error;
    }
  }

  /**
   * Get booking by ID with full details
   */
  async getBookingById(bookingId: string): Promise<BookingResponse> {
    try {
      const booking = await this.bookingRepository.findOne({
        where: { id: bookingId },
        relations: [
          'salon',
          'customer',
          'service',
          'staffSalonAccess',
          'staffSalonAccess.staff',
        ],
      });

      if (!booking) {
        throw new ApiError(404, 'Booking not found');
      }

      return this.formatBookingResponse(booking);
    } catch (error: any) {
      console.error('❌ BookingService: Error getting booking:', error);
      throw error;
    }
  }

  /**
   * Get bookings with filters
   */
  async getBookings(filters: BookingFilters = {}): Promise<BookingListResponse> {
    try {
      console.log('🔍 BookingService: Getting bookings with filters:', filters);

      const queryBuilder = this.bookingRepository
        .createQueryBuilder('booking')
        .leftJoinAndSelect('booking.salon', 'salon')
        .leftJoinAndSelect('booking.customer', 'customer')
        .leftJoinAndSelect('booking.service', 'service')
        .leftJoinAndSelect('booking.staffSalonAccess', 'staffSalonAccess')
        .leftJoinAndSelect('staffSalonAccess.staff', 'staff');

      // Apply filters
      this.applyBookingFilters(queryBuilder, filters);

      // Apply sorting
      const sortBy = filters.sortBy || 'bookingDate';
      const sortOrder = filters.sortOrder || 'DESC';
      queryBuilder.orderBy(`booking.${sortBy}`, sortOrder);

      // Apply pagination
      const limit = filters.limit || 20;
      const offset = filters.offset || 0;
      const page = Math.floor(offset / limit) + 1;

      queryBuilder.skip(offset).take(limit);

      // Execute query
      const [bookings, total] = await queryBuilder.getManyAndCount();

      // Format response
      const data = bookings.map(booking => this.formatBookingResponse(booking));
      const totalPages = Math.ceil(total / limit);

      return {
        data,
        total,
        page,
        totalPages,
        hasNext: page < totalPages,
        hasPrev: page > 1,
      };
    } catch (error: any) {
      console.error('❌ BookingService: Error getting bookings:', error);
      throw error;
    }
  }

  /**
   * Update booking status
   */
  async updateBookingStatus(
    bookingId: string,
    status: BookingStatus,
    notes?: string
  ): Promise<BookingResponse> {
    try {
      const booking = await this.bookingRepository.findOne({
        where: { id: bookingId }
      });

      if (!booking) {
        throw new ApiError(404, 'Booking not found');
      }

      // Update status and related timestamps
      booking.status = status;
      if (notes) booking.notes = notes;

      switch (status) {
        case BookingStatus.CONFIRMED:
          booking.confirmedAt = new Date();
          break;
        case BookingStatus.COMPLETED:
          booking.completedAt = new Date();
          break;
        case BookingStatus.CANCELLED:
          booking.cancelledAt = new Date();
          break;
      }

      await this.bookingRepository.save(booking);
      
      return await this.getBookingById(bookingId);
    } catch (error: any) {
      console.error('❌ BookingService: Error updating booking status:', error);
      throw error;
    }
  }

  /**
   * Cancel booking
   */
  async cancelBooking(bookingId: string, reason: string): Promise<BookingResponse> {
    try {
      const booking = await this.bookingRepository.findOne({
        where: { id: bookingId }
      });

      if (!booking) {
        throw new ApiError(404, 'Booking not found');
      }

      if (booking.status === BookingStatus.COMPLETED) {
        throw new ApiError(400, 'Cannot cancel completed booking');
      }

      booking.status = BookingStatus.CANCELLED;
      booking.cancellationReason = reason;
      booking.cancelledAt = new Date();

      await this.bookingRepository.save(booking);
      
      return await this.getBookingById(bookingId);
    } catch (error: any) {
      console.error('❌ BookingService: Error cancelling booking:', error);
      throw error;
    }
  }

  /**
   * Get staff availability for a specific date
   */
  private async getStaffAvailabilityForDate(
    staffSalonAccessId: string,
    date: string
  ): Promise<StaffAvailability[]> {
    try {
      const dateObj = new Date(date);
      const dayOfWeek = dateObj.toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();

      // Get both regular schedule and date-specific availability
      const availability = await this.staffAvailabilityRepository.find({
        where: [
          {
            staffSalonAccessId,
            type: AvailabilityType.REGULAR,
            dayOfWeek,
            status: AvailabilityStatus.ACTIVE,
          },
          {
            staffSalonAccessId,
            type: AvailabilityType.DATE_SPECIFIC,
            date: dateObj,
            status: AvailabilityStatus.ACTIVE,
          },
        ],
      });

      return availability;
    } catch (error: any) {
      console.error('❌ BookingService: Error getting staff availability:', error);
      return [];
    }
  }

  /**
   * Generate available time slots for staff
   */
  private async generateTimeSlots(
    availability: StaffAvailability[],
    serviceDuration: number,
    date: string,
    staffSalonAccessId: string
  ): Promise<TimeSlot[]> {
    try {
      const slots: TimeSlot[] = [];
      const slotDuration = 30; // 30-minute slots

      // Get existing bookings for this staff on this date
      const existingBookings = await this.bookingRepository.find({
        where: {
          staffSalonAccessId,
          bookingDate: new Date(date),
          status: In([BookingStatus.CONFIRMED, BookingStatus.IN_PROGRESS]),
        },
      });

      for (const avail of availability) {
        if (!avail.timeSlots || avail.timeSlots.length === 0) continue;

        for (const timeSlot of avail.timeSlots) {
          if (!timeSlot.isAvailable) continue;

          const startTime = this.parseTime(timeSlot.startTime);
          const endTime = this.parseTime(timeSlot.endTime);

          // Generate slots within this time range
          let currentTime = startTime;

          while (currentTime + serviceDuration <= endTime) {
            const slotStart = this.formatTime(currentTime);
            const slotEnd = this.formatTime(currentTime + serviceDuration);

            // Check if this slot conflicts with existing bookings
            const hasConflict = existingBookings.some(booking => {
              const bookingStart = this.parseTime(booking.startTime);
              const bookingEnd = this.parseTime(booking.endTime);

              return (currentTime < bookingEnd && currentTime + serviceDuration > bookingStart);
            });

            slots.push({
              startTime: slotStart,
              endTime: slotEnd,
              isAvailable: !hasConflict,
            });

            currentTime += slotDuration;
          }
        }
      }

      // Remove duplicates and sort
      const uniqueSlots = slots.filter((slot, index, self) =>
        index === self.findIndex(s => s.startTime === slot.startTime)
      );

      return uniqueSlots.sort((a, b) => a.startTime.localeCompare(b.startTime));
    } catch (error: any) {
      console.error('❌ BookingService: Error generating time slots:', error);
      return [];
    }
  }

  /**
   * Validate booking request
   */
  private async validateBooking(data: CreateBookingRequest): Promise<BookingValidationResult> {
    const errors: string[] = [];
    const warnings: string[] = [];

    try {
      // Check if salon exists
      const salon = await this.salonRepository.findOne({
        where: { id: data.salonId, isActive: true }
      });
      if (!salon) {
        errors.push('Salon not found or inactive');
      }

      // Check if customer exists
      const customer = await this.userRepository.findOne({
        where: { id: data.customerId }
      });
      if (!customer) {
        errors.push('Customer not found');
      }

      // Check if service exists
      const service = await this.serviceRepository.findOne({
        where: { id: data.serviceId, salonId: data.salonId, isActive: true }
      });
      if (!service) {
        errors.push('Service not found or inactive');
      }

      // Check if staff is assigned to this service
      const currentDate = new Date();
      const staffAssignment = await this.staffAssignmentRepository
        .createQueryBuilder('assignment')
        .where('assignment.serviceId = :serviceId', { serviceId: data.serviceId })
        .andWhere('assignment.staffSalonAccessId = :staffSalonAccessId', { staffSalonAccessId: data.staffSalonAccessId })
        .andWhere('assignment.status = :status', { status: AssignmentStatus.ACTIVE })
        .andWhere('assignment.isBookable = :isBookable', { isBookable: true })
        .andWhere('assignment.startDate <= :currentDate', { currentDate })
        .andWhere('(assignment.endDate IS NULL OR assignment.endDate > :currentDate)', { currentDate })
        .getOne();
      if (!staffAssignment) {
        errors.push('Staff is not assigned to this service or not bookable');
      }

      // Check for time conflicts
      const conflictingBooking = await this.bookingRepository.findOne({
        where: {
          staffSalonAccessId: data.staffSalonAccessId,
          bookingDate: new Date(data.bookingDate),
          status: In([BookingStatus.CONFIRMED, BookingStatus.IN_PROGRESS]),
        },
      });

      if (conflictingBooking) {
        const bookingStart = this.parseTime(conflictingBooking.startTime);
        const bookingEnd = this.parseTime(conflictingBooking.endTime);
        const requestStart = this.parseTime(data.startTime);
        const requestEnd = this.parseTime(data.endTime);

        if (requestStart < bookingEnd && requestEnd > bookingStart) {
          errors.push('Time slot conflicts with existing booking');
        }
      }

      return {
        isValid: errors.length === 0,
        errors,
        warnings,
        conflicts: {
          staffConflict: false,
          timeConflict: conflictingBooking !== null,
          serviceConflict: !staffAssignment,
        },
      };
    } catch (error: any) {
      console.error('❌ BookingService: Error validating booking:', error);
      return {
        isValid: false,
        errors: ['Validation error occurred'],
        warnings,
        conflicts: {
          staffConflict: false,
          timeConflict: false,
          serviceConflict: false,
        },
      };
    }
  }

  /**
   * Apply filters to booking query
   */
  private applyBookingFilters(
    queryBuilder: SelectQueryBuilder<Booking>,
    filters: BookingFilters
  ): void {
    if (filters.salonId) {
      queryBuilder.andWhere('booking.salonId = :salonId', { salonId: filters.salonId });
    }

    if (filters.customerId) {
      queryBuilder.andWhere('booking.customerId = :customerId', { customerId: filters.customerId });
    }

    if (filters.serviceId) {
      queryBuilder.andWhere('booking.serviceId = :serviceId', { serviceId: filters.serviceId });
    }

    if (filters.staffSalonAccessId) {
      queryBuilder.andWhere('booking.staffSalonAccessId = :staffSalonAccessId', {
        staffSalonAccessId: filters.staffSalonAccessId,
      });
    }

    if (filters.status) {
      queryBuilder.andWhere('booking.status = :status', { status: filters.status });
    }

    if (filters.paymentStatus) {
      queryBuilder.andWhere('booking.paymentStatus = :paymentStatus', {
        paymentStatus: filters.paymentStatus,
      });
    }

    if (filters.dateFrom) {
      queryBuilder.andWhere('booking.bookingDate >= :dateFrom', {
        dateFrom: filters.dateFrom,
      });
    }

    if (filters.dateTo) {
      queryBuilder.andWhere('booking.bookingDate <= :dateTo', {
        dateTo: filters.dateTo,
      });
    }

    if (filters.search) {
      queryBuilder.andWhere(
        '(booking.bookingNumber ILIKE :search OR service.name ILIKE :search OR customer.firstName ILIKE :search OR customer.lastName ILIKE :search)',
        { search: `%${filters.search}%` }
      );
    }
  }

  /**
   * Format booking response
   */
  private formatBookingResponse(booking: Booking): BookingResponse {
    return {
      id: booking.id,
      bookingNumber: booking.bookingNumber,
      status: booking.status,
      paymentStatus: booking.paymentStatus,
      service: {
        id: booking.service.id,
        name: booking.service.name,
        category: booking.service.category,
        duration: booking.service.duration,
        price: booking.service.price ?? 0,
        image: booking.service.image,
      },
      staff: {
        staffSalonAccessId: booking.staffSalonAccess.id,
        firstName: booking.staffSalonAccess.staff.firstName,
        lastName: booking.staffSalonAccess.staff.lastName,
        profileImage: booking.staffSalonAccess.staff.profileImage,
        position: booking.staffSalonAccess.position || 'Staff Member',
      },
      salon: {
        id: booking.salon.id,
        name: booking.salon.name,
        address: booking.salon.address ?? '',
        phone: booking.salon.phone ?? '',
        image: booking.salon?.logo,
      },
      customer: {
        id: booking.customer.id,
        firstName: booking.customer.firstName,
        lastName: booking.customer.lastName,
        email: booking.customer.email,
        phone: booking.customer.phoneNumber,
      },
      bookingDate: booking.bookingDate.toISOString().split('T')[0],
      startTime: booking.startTime,
      endTime: booking.endTime,
      duration: booking.duration,
      originalPrice: Number(booking.originalPrice),
      discountAmount: Number(booking.discountAmount),
      finalPrice: Number(booking.finalPrice),
      notes: booking.notes,
      specialRequests: booking.specialRequests,
      cancellationReason: booking.cancellationReason,
      createdAt: booking.createdAt.toISOString(),
      updatedAt: booking.updatedAt.toISOString(),
      confirmedAt: booking.confirmedAt?.toISOString(),
      completedAt: booking.completedAt?.toISOString(),
      cancelledAt: booking.cancelledAt?.toISOString(),
    };
  }

  /**
   * Parse time string to minutes
   */
  private parseTime(timeStr: string): number {
    const [hours, minutes] = timeStr.split(':').map(Number);
    return hours * 60 + minutes;
  }

  /**
   * Format minutes to time string
   */
  private formatTime(minutes: number): string {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}`;
  }
}

export const bookingService = new BookingService();
