import { Request, Response } from 'express';
import { validationResult } from 'express-validator';
import { 
  staffServiceAssignmentService, 
  CreateStaffServiceAssignmentRequest,
  UpdateStaffServiceAssignmentRequest,
  BulkAssignServicesRequest,
  AssignmentSearchFilters 
} from '../services/staffServiceAssignmentService';
import { AssignmentStatus, SpecializationLevel } from '../entities/StaffServiceAssignment';
import ApiError from '../utils/apiError';
import { IUser } from '@/types/user';

interface AuthenticatedRequest extends Request {
  user?: IUser;
}

class StaffServiceAssignmentController {
  /**
   * Create a new staff-service assignment
   */
  async createAssignment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      // Check validation errors
      const errors = validationResult(req);
      if (!errors.isEmpty()) {
        res.status(400).json({
          success: false,
          message: 'Validation failed',
          errors: errors.array(),
        });
        return;
      }

      const assignmentData: CreateStaffServiceAssignmentRequest = {
        ...req.body,
        assignedBy: req.user!.id,
      };

      const assignment = await staffServiceAssignmentService.createAssignment(assignmentData);

      res.status(201).json({
        success: true,
        data: assignment,
      });
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentController: Error creating assignment:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Get assignments with filters
   */
  async getAssignments(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const filters: AssignmentSearchFilters = {
        staffSalonAccessId: req.query.staffSalonAccessId as string,
        serviceId: req.query.serviceId as string,
        salonId: req.query.salonId as string,
        status: req.query.status as AssignmentStatus,
        specializationLevel: req.query.specializationLevel as SpecializationLevel,
        isBookable: req.query.isBookable === 'true' ? true : req.query.isBookable === 'false' ? false : undefined,
        search: req.query.search as string,
        sortBy: req.query.sortBy as any,
        sortOrder: req.query.sortOrder as 'ASC' | 'DESC',
        page: req.query.page ? parseInt(req.query.page as string) : undefined,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
      };

      const result = await staffServiceAssignmentService.getAssignments(filters);

      res.status(200).json({
        success: true,
        data: result.data,
        pagination: {
          total: result.total,
          page: result.page,
          totalPages: result.totalPages,
          limit: filters.limit || 20,
        },
      });
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentController: Error getting assignments:', error);
      
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  /**
   * Get assignment by ID
   */
  async getAssignmentById(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { assignmentId } = req.params;

      const assignment = await staffServiceAssignmentService.getAssignmentById(assignmentId);

      res.status(200).json({
        success: true,
        data: assignment,
      });
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentController: Error getting assignment:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Update assignment
   */
  async updateAssignment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { assignmentId } = req.params;

      const updateData: UpdateStaffServiceAssignmentRequest = {
        ...req.body,
        lastModifiedBy: req.user!.id,
      };

      const assignment = await staffServiceAssignmentService.updateAssignment(assignmentId, updateData);

      res.status(200).json({
        success: true,
        data: assignment,
      });
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentController: Error updating assignment:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Delete assignment
   */
  async deleteAssignment(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { assignmentId } = req.params;

      await staffServiceAssignmentService.deleteAssignment(assignmentId);

      res.status(200).json({
        success: true,
        message: 'Assignment deleted successfully',
      });
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentController: Error deleting assignment:', error);
      
      if (error instanceof ApiError) {
        res.status(error.statusCode).json({
          success: false,
          message: error.message,
        });
      } else {
        res.status(500).json({
          success: false,
          message: 'Internal server error',
        });
      }
    }
  }

  /**
   * Bulk assign services to staff
   */
  async bulkAssignServices(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const bulkData: BulkAssignServicesRequest = {
        ...req.body,
        assignedBy: req.user!.id,
      };

      const assignments = await staffServiceAssignmentService.bulkAssignServices(bulkData);

      res.status(201).json({
        success: true,
        data: assignments,
        message: `Successfully assigned ${assignments.length} services`,
      });
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentController: Error in bulk assignment:', error);
      
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  /**
   * Get services assigned to staff
   */
  async getStaffServices(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { staffSalonAccessId } = req.params;

      const assignments = await staffServiceAssignmentService.getStaffServices(staffSalonAccessId);

      res.status(200).json({
        success: true,
        data: assignments,
      });
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentController: Error getting staff services:', error);
      
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }

  /**
   * Get staff assigned to service
   */
  async getServiceStaff(req: AuthenticatedRequest, res: Response): Promise<void> {
    try {
      const { serviceId } = req.params;

      const assignments = await staffServiceAssignmentService.getServiceStaff(serviceId);

      res.status(200).json({
        success: true,
        data: assignments,
      });
    } catch (error: any) {
      console.error('❌ StaffServiceAssignmentController: Error getting service staff:', error);
      
      res.status(500).json({
        success: false,
        message: 'Internal server error',
      });
    }
  }
}

export const staffServiceAssignmentController = new StaffServiceAssignmentController();
