import { apiService } from './api';
import { ENDPOINTS } from '../config/api';
import {
  BookingResponse,
  BookingListResponse,
  CreateBookingRequest,
  BookingFilters,
  AvailabilityRequest,
  AvailabilityResponse,
  BookingStats,
  BookingStatus,
} from '../types/booking';

class BookingService {
  /**
   * Get available time slots for a service on a specific date
   */
  async getAvailability(request: AvailabilityRequest): Promise<AvailabilityResponse> {
    try {
      console.log('🔍 BookingService: Getting availability:', request);

      const queryParams = new URLSearchParams({
        salonId: request.salonId,
        serviceId: request.serviceId,
        date: request.date,
      });

      if (request.preferredStaffId) {
        queryParams.append('preferredStaffId', request.preferredStaffId);
      }

      const response = await apiService.get<AvailabilityResponse>(
        `${ENDPOINTS.BOOKINGS}/availability?${queryParams.toString()}`
      );

      console.log('✅ BookingService: Retrieved availability:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error getting availability:', error);
      throw error;
    }
  }

  /**
   * Create a new booking
   */
  async createBooking(bookingData: CreateBookingRequest): Promise<BookingResponse> {
    try {
      console.log('🔍 BookingService: Creating booking:', bookingData);

      const response = await apiService.post<BookingResponse>(
        ENDPOINTS.BOOKINGS,
        bookingData
      );

      console.log('✅ BookingService: Booking created:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error creating booking:', error);
      throw error;
    }
  }

  /**
   * Get bookings with filters
   */
  async getBookings(filters: BookingFilters = {}): Promise<BookingListResponse> {
    try {
      console.log('🔍 BookingService: Getting bookings with filters:', filters);

      const queryParams = new URLSearchParams();

      // Add filters to query params
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });

      const response = await apiService.get<BookingListResponse>(
        `${ENDPOINTS.BOOKINGS}?${queryParams.toString()}`
      );

      console.log('✅ BookingService: Retrieved bookings:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error getting bookings:', error);
      throw error;
    }
  }

  /**
   * Get customer bookings
   */
  async getCustomerBookings(customerId: string, filters: Omit<BookingFilters, 'customerId'> = {}): Promise<BookingListResponse> {
    try {
      console.log('🔍 BookingService: Getting customer bookings:', customerId);

      return await this.getBookings({
        ...filters,
        customerId,
      });
    } catch (error: any) {
      console.error('❌ BookingService: Error getting customer bookings:', error);
      throw error;
    }
  }

  /**
   * Get salon bookings
   */
  async getSalonBookings(salonId: string, filters: BookingFilters = {}): Promise<BookingListResponse> {
    try {
      console.log('🔍 BookingService: Getting salon bookings:', salonId);

      const queryParams = new URLSearchParams();

      // Add salon ID and filters
      queryParams.append('salonId', salonId);
      Object.entries(filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null && value !== '') {
          queryParams.append(key, value.toString());
        }
      });

      const response = await apiService.get<BookingListResponse>(
        `${ENDPOINTS.BOOKINGS}/salon?${queryParams.toString()}`
      );

      console.log('✅ BookingService: Retrieved salon bookings:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error getting salon bookings:', error);
      throw error;
    }
  }

  /**
   * Get booking by ID
   */
  async getBookingById(bookingId: string): Promise<BookingResponse> {
    try {
      console.log('🔍 BookingService: Getting booking by ID:', bookingId);

      const response = await apiService.get<BookingResponse>(
        `${ENDPOINTS.BOOKINGS}/${bookingId}`
      );

      console.log('✅ BookingService: Retrieved booking:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error getting booking:', error);
      throw error;
    }
  }

  /**
   * Update booking status
   */
  async updateBookingStatus(bookingId: string, status: BookingStatus, notes?: string): Promise<BookingResponse> {
    try {
      console.log('🔍 BookingService: Updating booking status:', bookingId, status);

      const response = await apiService.patch<BookingResponse>(
        `${ENDPOINTS.BOOKINGS}/${bookingId}/status`,
        { status, notes }
      );

      console.log('✅ BookingService: Booking status updated:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error updating booking status:', error);
      throw error;
    }
  }

  /**
   * Cancel booking
   */
  async cancelBooking(bookingId: string, reason: string): Promise<BookingResponse> {
    try {
      console.log('🔍 BookingService: Cancelling booking:', bookingId);

      const response = await apiService.patch<BookingResponse>(
        `${ENDPOINTS.BOOKINGS}/${bookingId}/cancel`,
        { cancellationReason: reason }
      );

      console.log('✅ BookingService: Booking cancelled:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error cancelling booking:', error);
      throw error;
    }
  }

  /**
   * Reschedule booking
   */
  async rescheduleBooking(
    bookingId: string,
    newDate: string,
    newStartTime: string,
    newStaffId?: string
  ): Promise<BookingResponse> {
    try {
      console.log('🔍 BookingService: Rescheduling booking:', bookingId);

      const response = await apiService.patch<BookingResponse>(
        `${ENDPOINTS.BOOKINGS}/${bookingId}/reschedule`,
        {
          bookingDate: newDate,
          startTime: newStartTime,
          staffSalonAccessId: newStaffId,
        }
      );

      console.log('✅ BookingService: Booking rescheduled:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error rescheduling booking:', error);
      throw error;
    }
  }

  /**
   * Get booking statistics
   */
  async getBookingStats(salonId: string, dateFrom?: string, dateTo?: string): Promise<BookingStats> {
    try {
      console.log('🔍 BookingService: Getting booking stats:', salonId);

      const queryParams = new URLSearchParams({ salonId });
      if (dateFrom) queryParams.append('dateFrom', dateFrom);
      if (dateTo) queryParams.append('dateTo', dateTo);

      const response = await apiService.get<BookingStats>(
        `${ENDPOINTS.BOOKINGS}/stats?${queryParams.toString()}`
      );

      console.log('✅ BookingService: Retrieved booking stats:', response.data);
      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error getting booking stats:', error);
      throw error;
    }
  }

  /**
   * Check if time slot is available
   */
  async checkSlotAvailability(
    salonId: string,
    serviceId: string,
    staffId: string,
    date: string,
    startTime: string
  ): Promise<boolean> {
    try {
      console.log('🔍 BookingService: Checking slot availability');

      const queryParams = new URLSearchParams({
        salonId,
        serviceId,
        staffId,
        date,
        startTime,
      });

      const response = await apiService.get<{ available: boolean }>(
        `${ENDPOINTS.BOOKINGS}/check-availability?${queryParams.toString()}`
      );

      return response.data.available;
    } catch (error: any) {
      console.error('❌ BookingService: Error checking slot availability:', error);
      return false;
    }
  }

  /**
   * Get upcoming bookings for customer
   */
  async getUpcomingBookings(customerId: string, limit: number = 5): Promise<BookingResponse[]> {
    try {
      const today = new Date().toISOString().split('T')[0];
      
      const response = await this.getCustomerBookings(customerId, {
        dateFrom: today,
        status: BookingStatus.CONFIRMED,
        sortBy: 'bookingDate',
        sortOrder: 'ASC',
        limit,
      });

      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error getting upcoming bookings:', error);
      throw error;
    }
  }

  /**
   * Get booking history for customer
   */
  async getBookingHistory(customerId: string, limit: number = 10): Promise<BookingResponse[]> {
    try {
      const response = await this.getCustomerBookings(customerId, {
        status: BookingStatus.COMPLETED,
        sortBy: 'bookingDate',
        sortOrder: 'DESC',
        limit,
      });

      return response.data;
    } catch (error: any) {
      console.error('❌ BookingService: Error getting booking history:', error);
      throw error;
    }
  }
}

export const bookingService = new BookingService();
