import { Request, Response } from 'express';
import { 
  staffAvailabilityService,
  CreateAvailabilityRequest,
  UpdateAvailabilityRequest,
  ApproveAvailabilityRequest,
  AvailabilitySearchFilters,
  BulkCreateAvailabilityRequest
} from '../services/staffAvailabilityService';
import ApiError from '../utils/apiError';
import { AvailabilityType, AvailabilityStatus } from '../entities/StaffAvailability';

/**
 * Get staff availability records
 */
export const getAvailability = async (req: Request, res: Response) => {
  try {
    console.log('🔍 StaffAvailabilityController: Getting availability with filters:', req.query);

    const filters: AvailabilitySearchFilters = {
      staffSalonAccessId: req.query.staffSalonAccessId as string,
      salonId: req.query.salonId as string,
      type: req.query.type as AvailabilityType,
      status: req.query.status as AvailabilityStatus,
      date: req.query.date ? parseDate(req.query.date as string) : undefined,
      dateFrom: req.query.dateFrom ? parseDate(req.query.dateFrom as string) : undefined,
      dateTo: req.query.dateTo ? parseDate(req.query.dateTo as string) : undefined,
      dayOfWeek: req.query.dayOfWeek as string,
      isAvailable: req.query.isAvailable ? req.query.isAvailable === 'true' : undefined,
      pendingApproval: req.query.pendingApproval ? req.query.pendingApproval === 'true' : undefined,
      page: req.query.page ? parseInt(req.query.page as string) : undefined,
      limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
    };

    // Helper function to safely parse dates
    function parseDate(dateString: string): Date | undefined {
      try {
        // Handle both YYYY-MM-DD and ISO string formats
        const date = new Date(dateString);
        if (isNaN(date.getTime())) {
          console.warn('Invalid date format:', dateString);
          return undefined;
        }
        return date;
      } catch (error) {
        console.warn('Error parsing date:', dateString, error);
        return undefined;
      }
    }

    console.log('🔍 StaffAvailabilityController: Searching with filters:', filters);
    const result = await staffAvailabilityService.getAvailability(filters);

    console.log('✅ StaffAvailabilityController: Retrieved availability:', result.data.length);
    res.status(200).json({
      success: true,
      data: result,
    });
  } catch (error: any) {
    console.error('❌ StaffAvailabilityController: Error getting availability:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to get availability',
    });
  }
};

/**
 * Get availability by ID
 */
export const getAvailabilityById = async (req: Request, res: Response) => {
  try {
    const { availabilityId } = req.params;
    console.log('🔍 StaffAvailabilityController: Getting availability by ID:', availabilityId);

    const availability = await staffAvailabilityService.getAvailabilityById(availabilityId);

    console.log('✅ StaffAvailabilityController: Retrieved availability by ID');
    res.status(200).json({
      success: true,
      data: availability,
    });
  } catch (error: any) {
    console.error('❌ StaffAvailabilityController: Error getting availability by ID:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to get availability',
    });
  }
};

/**
 * Create new availability record
 */
export const createAvailability = async (req: Request, res: Response) => {
  try {
    console.log('🔍 StaffAvailabilityController: Creating availability:', req.body);

    const data: CreateAvailabilityRequest = {
      ...req.body,
      createdBy: req.user?.id || 'system',
    };

    const availability = await staffAvailabilityService.createAvailability(data);

    console.log('✅ StaffAvailabilityController: Created availability:', availability.id);
    res.status(201).json({
      success: true,
      data: availability,
    });
  } catch (error: any) {
    console.error('❌ StaffAvailabilityController: Error creating availability:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to create availability',
    });
  }
};

/**
 * Update availability record
 */
export const updateAvailability = async (req: Request, res: Response) => {
  try {
    const { availabilityId } = req.params;
    console.log('🔍 StaffAvailabilityController: Updating availability:', availabilityId, req.body);

    const data: UpdateAvailabilityRequest = {
      ...req.body,
      lastModifiedBy: req.user?.id || 'system',
    };

    const availability = await staffAvailabilityService.updateAvailability(availabilityId, data);

    console.log('✅ StaffAvailabilityController: Updated availability:', availability.id);
    res.status(200).json({
      success: true,
      data: availability,
    });
  } catch (error: any) {
    console.error('❌ StaffAvailabilityController: Error updating availability:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to update availability',
    });
  }
};

/**
 * Delete availability record
 */
export const deleteAvailability = async (req: Request, res: Response) => {
  try {
    const { availabilityId } = req.params;
    console.log('🔍 StaffAvailabilityController: Deleting availability:', availabilityId);

    await staffAvailabilityService.deleteAvailability(availabilityId);

    console.log('✅ StaffAvailabilityController: Deleted availability:', availabilityId);
    res.status(200).json({
      success: true,
      message: 'Availability deleted successfully',
    });
  } catch (error: any) {
    console.error('❌ StaffAvailabilityController: Error deleting availability:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to delete availability',
    });
  }
};

/**
 * Approve or reject availability
 */
export const approveAvailability = async (req: Request, res: Response) => {
  try {
    const { availabilityId } = req.params;
    console.log('🔍 StaffAvailabilityController: Approving availability:', availabilityId, req.body);

    const data: ApproveAvailabilityRequest = {
      ...req.body,
      approvedBy: req.user?.id || 'system',
    };

    const availability = await staffAvailabilityService.approveAvailability(availabilityId, data);

    console.log('✅ StaffAvailabilityController: Approved availability:', availability.id);
    res.status(200).json({
      success: true,
      data: availability,
    });
  } catch (error: any) {
    console.error('❌ StaffAvailabilityController: Error approving availability:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to approve availability',
    });
  }
};

/**
 * Bulk create availability
 */
export const bulkCreateAvailability = async (req: Request, res: Response) => {
  try {
    console.log('🔍 StaffAvailabilityController: Bulk creating availability:', req.body);

    const data: BulkCreateAvailabilityRequest = {
      ...req.body,
      createdBy: req.user?.id || 'system',
    };

    const availabilities = await staffAvailabilityService.bulkCreateAvailability(data);

    console.log('✅ StaffAvailabilityController: Bulk created availability:', availabilities.length);
    res.status(201).json({
      success: true,
      data: availabilities,
    });
  } catch (error: any) {
    console.error('❌ StaffAvailabilityController: Error bulk creating availability:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to bulk create availability',
    });
  }
};

/**
 * Block time slots for specific date
 */
export const blockTimeSlots = async (req: Request, res: Response) => {
  try {
    console.log('🔍 StaffAvailabilityController: Blocking time slots:', req.body);

    const { staffSalonAccessId, date, blockedSlots, notes } = req.body;

    if (!staffSalonAccessId || !date || !blockedSlots || !Array.isArray(blockedSlots)) {
      throw new ApiError(400, 'Missing required fields: staffSalonAccessId, date, blockedSlots');
    }

    // Parse date safely
    const parsedDate = new Date(date);
    if (isNaN(parsedDate.getTime())) {
      throw new ApiError(400, 'Invalid date format');
    }

    // Find existing availability or create override
    let availability = await staffAvailabilityService.getAvailability({
      staffSalonAccessId,
      date: parsedDate,
      status: AvailabilityStatus.ACTIVE,
    });

    let targetAvailability;
    if (availability.data.length > 0) {
      // Update existing availability
      targetAvailability = availability.data[0];

      console.log('🔄 Updating existing availability with', blockedSlots.length, 'blocked slots');
      console.log('📊 Current time slots:', targetAvailability.timeSlots.length);

      // Start with existing time slots
      let updatedTimeSlots = [...targetAvailability.timeSlots];

      // Process each blocked slot
      blockedSlots.forEach((blockedSlot: any) => {
        const existingSlotIndex = updatedTimeSlots.findIndex(slot =>
          slot.startTime === blockedSlot.startTime && slot.endTime === blockedSlot.endTime
        );

        if (existingSlotIndex >= 0) {
          // Update existing slot to blocked
          updatedTimeSlots[existingSlotIndex] = {
            ...updatedTimeSlots[existingSlotIndex],
            isAvailable: false,
            notes: blockedSlot.notes || 'Blocked by admin'
          };
          console.log('✅ Updated existing slot:', blockedSlot.startTime, '-', blockedSlot.endTime);
        } else {
          // Add new blocked slot
          updatedTimeSlots.push({
            startTime: blockedSlot.startTime,
            endTime: blockedSlot.endTime,
            isAvailable: false,
            notes: blockedSlot.notes || 'Blocked by admin'
          });
          console.log('➕ Added new blocked slot:', blockedSlot.startTime, '-', blockedSlot.endTime);
        }
      });

      console.log('📊 Final time slots:', updatedTimeSlots.length);

      targetAvailability = await staffAvailabilityService.updateAvailability(targetAvailability.id, {
        timeSlots: updatedTimeSlots,
        isAvailable: updatedTimeSlots.some(slot => slot.isAvailable),
        lastModifiedBy: req.user?.id || 'system',
        notes: notes || `Updated with ${blockedSlots.length} blocked slot${blockedSlots.length > 1 ? 's' : ''} on ${new Date(date).toLocaleDateString()}`,
      });
    } else {
      // Create new override availability
      const createData: CreateAvailabilityRequest = {
        staffSalonAccessId,
        type: AvailabilityType.OVERRIDE,
        date: parsedDate,
        timeSlots: blockedSlots.map((slot: any) => ({
          startTime: slot.startTime,
          endTime: slot.endTime,
          isAvailable: false,
          notes: 'Blocked by admin'
        })),
        isAvailable: false,
        notes: notes || `Blocked ${blockedSlots.length} time slot${blockedSlots.length > 1 ? 's' : ''} on ${parsedDate.toLocaleDateString()}`,
        createdBy: req.user?.id || 'system',
      };
      
      targetAvailability = await staffAvailabilityService.createAvailability(createData);
    }

    console.log('✅ StaffAvailabilityController: Blocked time slots successfully');
    res.status(200).json({
      success: true,
      data: targetAvailability,
    });
  } catch (error: any) {
    console.error('❌ StaffAvailabilityController: Error blocking time slots:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to block time slots',
    });
  }
};

/**
 * Unblock time slots for specific date
 */
export const unblockTimeSlots = async (req: Request, res: Response) => {
  try {
    console.log('🔍 StaffAvailabilityController: Unblocking time slots:', req.body);

    const { staffSalonAccessId, date, unblockedSlots, notes } = req.body;

    if (!staffSalonAccessId || !date || !unblockedSlots || !Array.isArray(unblockedSlots)) {
      throw new ApiError(400, 'Missing required fields: staffSalonAccessId, date, unblockedSlots');
    }

    // Parse date safely
    const parsedDate = new Date(date);
    if (isNaN(parsedDate.getTime())) {
      throw new ApiError(400, 'Invalid date format');
    }

    // Get existing availability for this date
    const availability = await staffAvailabilityService.getAvailability({
      staffSalonAccessId,
      date: parsedDate,
      status: AvailabilityStatus.ACTIVE,
    });

    if (availability.data.length === 0) {
      throw new ApiError(404, 'No availability record found for this date');
    }

    const targetAvailability = availability.data[0];
    
    // Update time slots to mark unblocked slots as available
    const updatedTimeSlots = targetAvailability.timeSlots.map(slot => {
      const isUnblocked = unblockedSlots.some((unblockedSlot: any) => 
        slot.startTime === unblockedSlot.startTime && slot.endTime === unblockedSlot.endTime
      );
      
      if (isUnblocked && slot.notes !== 'Break time') {
        return {
          ...slot,
          isAvailable: true,
          notes: undefined
        };
      }
      
      return slot;
    });
    
    const updatedAvailability = await staffAvailabilityService.updateAvailability(targetAvailability.id, {
      timeSlots: updatedTimeSlots,
      isAvailable: updatedTimeSlots.some(slot => slot.isAvailable),
      lastModifiedBy: req.user?.id || 'system',
      notes: notes || `Unblocked ${unblockedSlots.length} time slot${unblockedSlots.length > 1 ? 's' : ''} on ${parsedDate.toLocaleDateString()}`,
    });

    console.log('✅ StaffAvailabilityController: Unblocked time slots successfully');
    res.status(200).json({
      success: true,
      data: updatedAvailability,
    });
  } catch (error: any) {
    console.error('❌ StaffAvailabilityController: Error unblocking time slots:', error);
    res.status(error.statusCode || 500).json({
      success: false,
      message: error.message || 'Failed to unblock time slots',
    });
  }
};
