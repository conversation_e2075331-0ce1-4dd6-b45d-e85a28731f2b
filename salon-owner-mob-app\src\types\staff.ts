// Staff Types (matching backend)
export enum StaffAccessStatus {
  ACTIVE = 'ACTIVE',
  SUSPENDED = 'SUSPENDED',
  REVOKED = 'REVOKED',
}

export enum StaffPermission {
  VIEW_SERVICES = 'VIEW_SERVICES',
  <PERSON>NA<PERSON>_SERVICES = 'MANAGE_SERVICES',
  VIEW_OFFERS = 'VIEW_OFFERS',
  MANAGE_OFFERS = 'MANAGE_OFFERS',
  VIEW_BOOKINGS = 'VIEW_BOOKINGS',
  MANAGE_BOOKINGS = 'MANAGE_BOOKINGS',
  VIEW_CUSTOMERS = 'VIEW_CUSTOMERS',
  MANAGE_CUSTOMERS = 'MANA<PERSON>_CUSTOMERS',
  VIEW_REPORTS = 'VIEW_REPORTS',
  MANAGE_STAFF = 'MANAGE_STAFF',
}

export enum StaffPosition {
  // Management
  SALON_MANAGER = 'SALON_MANAGER',
  ASSISTANT_MANAGER = 'ASSISTANT_MANAGER',
  SUPERVISOR = 'SUPERVISOR',

  // Hair Services
  SENIOR_HAIR_STYLIST = 'SENIOR_HAIR_STYLIST',
  HAIR_STYLIST = 'HAIR_STYLIST',
  JUNIOR_HAIR_STYLIST = 'JUNIOR_HAIR_STYLIST',
  HAIR_COLORIST = 'HAIR_COLORIST',
  BARBER = 'BARBER',

  // Beauty Services
  ESTHETICIAN = 'ESTHETICIAN',
  MAKEUP_ARTIST = 'MAKEUP_ARTIST',
  EYEBROW_SPECIALIST = 'EYEBROW_SPECIALIST',
  LASH_TECHNICIAN = 'LASH_TECHNICIAN',

  // Nail Services
  NAIL_TECHNICIAN = 'NAIL_TECHNICIAN',
  NAIL_ARTIST = 'NAIL_ARTIST',

  // Spa Services
  MASSAGE_THERAPIST = 'MASSAGE_THERAPIST',
  SPA_THERAPIST = 'SPA_THERAPIST',

  // Support Staff
  RECEPTIONIST = 'RECEPTIONIST',
  ASSISTANT = 'ASSISTANT',
  APPRENTICE = 'APPRENTICE',
  CLEANER = 'CLEANER',

  // Other
  FREELANCER = 'FREELANCER',
  CONSULTANT = 'CONSULTANT',
  TRAINER = 'TRAINER',
  OTHER = 'OTHER',
}

export interface StaffUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  role: 'STAFF';
  profileImage?: string;
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface StaffSalonAccess {
  id: string;
  staffId: string;
  salonId: string;
  status: StaffAccessStatus;
  permissions: StaffPermission[];
  position?: string;
  hourlyRate?: number;
  workingHours?: Record<string, { start: string; end: string; isWorking: boolean }>;
  startDate?: Date;
  endDate?: Date;
  notes?: string;
  grantedBy: string;
  grantedAt: Date;
  lastModifiedBy?: string;
  createdAt: Date;
  updatedAt: Date;
  
  // Relationships
  staff?: StaffUser;
  salon?: {
    id: string;
    name: string;
    logo?: string;
    address?: string;
    city?: string;
  };
}

export interface StaffMember extends StaffSalonAccess {
  staff: StaffUser;
}

export interface CreateStaffRequest {
  firstName: string;
  lastName: string;
  email: string;
  phoneNumber?: string;
  password: string;
  salonIds: string[]; // Support multiple salons
  position?: StaffPosition;
  hourlyRate?: number;
  permissions: string[]; // Permission codes instead of enum
  workingHours?: Record<string, { start: string; end: string; isWorking: boolean }>;
  startDate?: Date;
  notes?: string;
  profileImage?: string; // Image URL from Azure blob storage
}

export interface UpdateStaffRequest {
  firstName?: string;
  lastName?: string;
  email?: string;
  phoneNumber?: string;
  position?: string;
  hourlyRate?: number;
  permissions?: string[]; // Permission codes instead of enum
  workingHours?: Record<string, { start: string; end: string; isWorking: boolean }>;
  status?: StaffAccessStatus;
  notes?: string;
  profileImage?: string; // Image URL from Azure blob storage
  salonId: string;
}

export interface StaffSearchFilters {
  salonId?: string;
  status?: StaffAccessStatus;
  position?: string;
  search?: string;
  sortBy?: 'firstName' | 'lastName' | 'position' | 'startDate' | 'createdAt';
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

export interface StaffPermissionInfo {
  id: string;
  code: string;
  name: string;
  description: string;
  category: string;
  categoryLabel: string;
  // Legacy support
  value?: string;
  label?: string;
}

export interface WorkingHours {
  monday: { start: string; end: string; isWorking: boolean };
  tuesday: { start: string; end: string; isWorking: boolean };
  wednesday: { start: string; end: string; isWorking: boolean };
  thursday: { start: string; end: string; isWorking: boolean };
  friday: { start: string; end: string; isWorking: boolean };
  saturday: { start: string; end: string; isWorking: boolean };
  sunday: { start: string; end: string; isWorking: boolean };
}

// Utility functions
export const formatStaffName = (staff: StaffUser): string => {
  return `${staff.firstName} ${staff.lastName}`;
};

export const formatPosition = (position?: string): string => {
  if (!position) return 'Staff Member';

  // If it's an enum value, format it nicely
  if (Object.values(StaffPosition).includes(position as StaffPosition)) {
    return position.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
  }

  // Otherwise return as is
  return position;
};

export const formatHourlyRate = (rate?: number): string => {
  return rate ? `$${rate.toFixed(2)}/hr` : 'Not set';
};

export const getStatusColor = (status: StaffAccessStatus): string => {
  switch (status) {
    case StaffAccessStatus.ACTIVE:
      return '#4CAF50';
    case StaffAccessStatus.SUSPENDED:
      return '#FF9800';
    case StaffAccessStatus.REVOKED:
      return '#F44336';
    default:
      return '#9E9E9E';
  }
};

export const getStatusLabel = (status: StaffAccessStatus): string => {
  switch (status) {
    case StaffAccessStatus.ACTIVE:
      return 'Active';
    case StaffAccessStatus.SUSPENDED:
      return 'Suspended';
    case StaffAccessStatus.REVOKED:
      return 'Revoked';
    default:
      return 'Unknown';
  }
};

export const getPermissionLabel = (permission: StaffPermission): string => {
  return permission.replace(/_/g, ' ').toLowerCase().replace(/\b\w/g, l => l.toUpperCase());
};

export const getPermissionDescription = (permission: StaffPermission): string => {
  const descriptions: Record<StaffPermission, string> = {
    [StaffPermission.VIEW_SERVICES]: 'View salon services and pricing',
    [StaffPermission.MANAGE_SERVICES]: 'Create, edit, and delete salon services',
    [StaffPermission.VIEW_OFFERS]: 'View salon offers and promotions',
    [StaffPermission.MANAGE_OFFERS]: 'Create, edit, and delete salon offers',
    [StaffPermission.VIEW_BOOKINGS]: 'View customer bookings and appointments',
    [StaffPermission.MANAGE_BOOKINGS]: 'Create, edit, and cancel bookings',
    [StaffPermission.VIEW_CUSTOMERS]: 'View customer information and history',
    [StaffPermission.MANAGE_CUSTOMERS]: 'Edit customer information and preferences',
    [StaffPermission.VIEW_REPORTS]: 'View salon analytics and reports',
    [StaffPermission.MANAGE_STAFF]: 'Manage other staff members (supervisor role)',
  };

  return descriptions[permission] || 'Permission description not available';
};

export const getDefaultWorkingHours = (): WorkingHours => {
  const defaultHours = { start: '09:00', end: '17:00', isWorking: true };
  const weekendHours = { start: '09:00', end: '17:00', isWorking: false };
  
  return {
    monday: defaultHours,
    tuesday: defaultHours,
    wednesday: defaultHours,
    thursday: defaultHours,
    friday: defaultHours,
    saturday: weekendHours,
    sunday: weekendHours,
  };
};

export const getDefaultPermissions = (): string[] => {
  return [
    'VIEW_SERVICES',
    'VIEW_BOOKINGS',
    'VIEW_CUSTOMERS',
  ];
};

export const validateStaffForm = (staff: Partial<CreateStaffRequest>): string[] => {
  const errors: string[] = [];

  if (!staff.firstName || staff.firstName.trim().length < 2) {
    errors.push('First name must be at least 2 characters long');
  }

  if (!staff.lastName || staff.lastName.trim().length < 2) {
    errors.push('Last name must be at least 2 characters long');
  }

  if (!staff.email || !/\S+@\S+\.\S+/.test(staff.email)) {
    errors.push('Valid email address is required');
  }

  if (!staff.password || staff.password.length < 6) {
    errors.push('Password must be at least 6 characters long');
  }

  if (!staff.salonIds || staff.salonIds.length === 0) {
    errors.push('At least one salon must be selected');
  }

  if (staff.hourlyRate && staff.hourlyRate < 0) {
    errors.push('Hourly rate cannot be negative');
  }

  if (!staff.permissions || staff.permissions.length === 0) {
    errors.push('At least one permission must be selected');
  }

  return errors;
};

export const validateUpdateStaffForm = (staff: Partial<UpdateStaffRequest>): string[] => {
  const errors: string[] = [];

  if (!staff.firstName || staff.firstName.trim().length < 2) {
    errors.push('First name must be at least 2 characters long');
  }

  if (!staff.lastName || staff.lastName.trim().length < 2) {
    errors.push('Last name must be at least 2 characters long');
  }

  if (!staff.email || !/\S+@\S+\.\S+/.test(staff.email)) {
    errors.push('Valid email address is required');
  }

  if (!staff.salonId) {
    errors.push('Salon ID is required');
  }

  if (staff.hourlyRate && staff.hourlyRate < 0) {
    errors.push('Hourly rate cannot be negative');
  }

  if (!staff.permissions || staff.permissions.length === 0) {
    errors.push('At least one permission must be selected');
  }

  return errors;
};

export const formatWorkingDay = (day: string): string => {
  return day.charAt(0).toUpperCase() + day.slice(1);
};

export const formatWorkingTime = (time: string): string => {
  const [hours, minutes] = time.split(':');
  const hour = parseInt(hours);
  const ampm = hour >= 12 ? 'PM' : 'AM';
  const displayHour = hour % 12 || 12;
  return `${displayHour}:${minutes} ${ampm}`;
};

export const isWorkingToday = (workingHours?: WorkingHours): boolean => {
  if (!workingHours) return false;
  
  const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
  const todayHours = workingHours[today as keyof WorkingHours];
  
  return todayHours?.isWorking || false;
};

export const getTodayWorkingHours = (workingHours?: WorkingHours): string => {
  if (!workingHours) return 'Not set';

  const today = new Date().toLocaleDateString('en-US', { weekday: 'long' }).toLowerCase();
  const todayHours = workingHours[today as keyof WorkingHours];

  if (!todayHours?.isWorking) return 'Not working today';

  return `${formatWorkingTime(todayHours.start)} - ${formatWorkingTime(todayHours.end)}`;
};

// Position utility functions
export interface PositionOption {
  value: StaffPosition;
  label: string;
  category: string;
}

export const getPositionOptions = (): PositionOption[] => {
  return [
    // Management
    { value: StaffPosition.SALON_MANAGER, label: 'Salon Manager', category: 'Management' },
    { value: StaffPosition.ASSISTANT_MANAGER, label: 'Assistant Manager', category: 'Management' },
    { value: StaffPosition.SUPERVISOR, label: 'Supervisor', category: 'Management' },

    // Hair Services
    { value: StaffPosition.SENIOR_HAIR_STYLIST, label: 'Senior Hair Stylist', category: 'Hair Services' },
    { value: StaffPosition.HAIR_STYLIST, label: 'Hair Stylist', category: 'Hair Services' },
    { value: StaffPosition.JUNIOR_HAIR_STYLIST, label: 'Junior Hair Stylist', category: 'Hair Services' },
    { value: StaffPosition.HAIR_COLORIST, label: 'Hair Colorist', category: 'Hair Services' },
    { value: StaffPosition.BARBER, label: 'Barber', category: 'Hair Services' },

    // Beauty Services
    { value: StaffPosition.ESTHETICIAN, label: 'Esthetician', category: 'Beauty Services' },
    { value: StaffPosition.MAKEUP_ARTIST, label: 'Makeup Artist', category: 'Beauty Services' },
    { value: StaffPosition.EYEBROW_SPECIALIST, label: 'Eyebrow Specialist', category: 'Beauty Services' },
    { value: StaffPosition.LASH_TECHNICIAN, label: 'Lash Technician', category: 'Beauty Services' },

    // Nail Services
    { value: StaffPosition.NAIL_TECHNICIAN, label: 'Nail Technician', category: 'Nail Services' },
    { value: StaffPosition.NAIL_ARTIST, label: 'Nail Artist', category: 'Nail Services' },

    // Spa Services
    { value: StaffPosition.MASSAGE_THERAPIST, label: 'Massage Therapist', category: 'Spa Services' },
    { value: StaffPosition.SPA_THERAPIST, label: 'Spa Therapist', category: 'Spa Services' },

    // Support Staff
    { value: StaffPosition.RECEPTIONIST, label: 'Receptionist', category: 'Support Staff' },
    { value: StaffPosition.ASSISTANT, label: 'Assistant', category: 'Support Staff' },
    { value: StaffPosition.APPRENTICE, label: 'Apprentice', category: 'Support Staff' },
    { value: StaffPosition.CLEANER, label: 'Cleaner', category: 'Support Staff' },

    // Other
    { value: StaffPosition.FREELANCER, label: 'Freelancer', category: 'Other' },
    { value: StaffPosition.CONSULTANT, label: 'Consultant', category: 'Other' },
    { value: StaffPosition.TRAINER, label: 'Trainer', category: 'Other' },
    { value: StaffPosition.OTHER, label: 'Other', category: 'Other' },
  ];
};

export const getPositionsByCategory = (): Record<string, PositionOption[]> => {
  const positions = getPositionOptions();
  const categorized: Record<string, PositionOption[]> = {};

  positions.forEach(position => {
    if (!categorized[position.category]) {
      categorized[position.category] = [];
    }
    categorized[position.category].push(position);
  });

  return categorized;
};

export const getPositionLabel = (position: StaffPosition): string => {
  const option = getPositionOptions().find(p => p.value === position);
  return option?.label || formatPosition(position);
};

export const getPositionCategory = (position: StaffPosition): string => {
  const option = getPositionOptions().find(p => p.value === position);
  return option?.category || 'Other';
};
