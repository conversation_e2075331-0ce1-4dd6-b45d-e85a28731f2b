import { Request, Response } from 'express';
import { bookingService } from '../services/bookingService';
import { BookingStatus } from '../entities/Booking';
import {
  CreateBookingRequest,
  UpdateBookingRequest,
  BookingFilters,
  AvailabilityRequest,
} from '../types/booking';
import ApiError from '../utils/apiError';
import { body, query, param } from 'express-validator';

class BookingController {
  /**
   * Get availability for a service
   */
  async getAvailability(req: Request, res: Response): Promise<void> {
    try {
      const { salonId, serviceId, date, preferredStaffId } = req.query;

      if (!salonId || !serviceId || !date) {
        throw new ApiError(400, 'salonId, serviceId, and date are required');
      }

      const request: AvailabilityRequest = {
        salonId: salonId as string,
        serviceId: serviceId as string,
        date: date as string,
        preferredStaffId: preferredStaffId as string,
      };

      const availability = await bookingService.getAvailability(request);

      res.status(200).json({
        success: true,
        data: availability,
      });
    } catch (error: any) {
      console.error('❌ BookingController: Error getting availability:', error);
      throw error;
    }
  }

  /**
   * Create a new booking
   */
  async createBooking(req: Request, res: Response): Promise<void> {
    try {
      const bookingData: CreateBookingRequest = req.body;

      // Validate required fields
      if (!bookingData.salonId || !bookingData.customerId || !bookingData.serviceId || 
          !bookingData.staffSalonAccessId || !bookingData.bookingDate || 
          !bookingData.startTime || !bookingData.endTime) {
        throw new ApiError(400, 'Missing required booking information');
      }

      const booking = await bookingService.createBooking(bookingData);

      res.status(201).json({
        success: true,
        data: booking,
      });
    } catch (error: any) {
      console.error('❌ BookingController: Error creating booking:', error);
      throw error;
    }
  }

  /**
   * Get bookings with filters
   */
  async getBookings(req: Request, res: Response): Promise<void> {
    try {
      const filters: BookingFilters = {
        salonId: req.query.salonId as string,
        customerId: req.query.customerId as string,
        serviceId: req.query.serviceId as string,
        staffSalonAccessId: req.query.staffSalonAccessId as string,
        status: req.query.status as BookingStatus,
        dateFrom: req.query.dateFrom as string,
        dateTo: req.query.dateTo as string,
        search: req.query.search as string,
        sortBy: req.query.sortBy as any,
        sortOrder: req.query.sortOrder as any,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined,
      };

      // Remove undefined values
      Object.keys(filters).forEach(key => {
        if (filters[key as keyof BookingFilters] === undefined) {
          delete filters[key as keyof BookingFilters];
        }
      });

      const bookings = await bookingService.getBookings(filters);

      res.status(200).json({
        success: true,
        data: bookings.data,
        pagination: {
          total: bookings.total,
          page: bookings.page,
          totalPages: bookings.totalPages,
          hasNext: bookings.hasNext,
          hasPrev: bookings.hasPrev,
        },
      });
    } catch (error: any) {
      console.error('❌ BookingController: Error getting bookings:', error);
      throw error;
    }
  }

  /**
   * Get salon bookings
   */
  async getSalonBookings(req: Request, res: Response): Promise<void> {
    try {
      const filters: BookingFilters = {
        ...req.query,
        limit: req.query.limit ? parseInt(req.query.limit as string) : undefined,
        offset: req.query.offset ? parseInt(req.query.offset as string) : undefined,
      } as BookingFilters;

      // Remove undefined values
      Object.keys(filters).forEach(key => {
        if (filters[key as keyof BookingFilters] === undefined) {
          delete filters[key as keyof BookingFilters];
        }
      });

      const bookings = await bookingService.getBookings(filters);

      res.status(200).json({
        success: true,
        data: bookings.data,
        pagination: {
          total: bookings.total,
          page: bookings.page,
          totalPages: bookings.totalPages,
          hasNext: bookings.hasNext,
          hasPrev: bookings.hasPrev,
        },
      });
    } catch (error: any) {
      console.error('❌ BookingController: Error getting salon bookings:', error);
      throw error;
    }
  }

  /**
   * Get booking by ID
   */
  async getBookingById(req: Request, res: Response): Promise<void> {
    try {
      const { bookingId } = req.params;

      if (!bookingId) {
        throw new ApiError(400, 'Booking ID is required');
      }

      const booking = await bookingService.getBookingById(bookingId);

      res.status(200).json({
        success: true,
        data: booking,
      });
    } catch (error: any) {
      console.error('❌ BookingController: Error getting booking:', error);
      throw error;
    }
  }

  /**
   * Update booking status
   */
  async updateBookingStatus(req: Request, res: Response): Promise<void> {
    try {
      const { bookingId } = req.params;
      const { status, notes } = req.body;

      if (!bookingId) {
        throw new ApiError(400, 'Booking ID is required');
      }

      if (!status) {
        throw new ApiError(400, 'Status is required');
      }

      const booking = await bookingService.updateBookingStatus(bookingId, status, notes);

      res.status(200).json({
        success: true,
        data: booking,
      });
    } catch (error: any) {
      console.error('❌ BookingController: Error updating booking status:', error);
      throw error;
    }
  }

  /**
   * Cancel booking
   */
  async cancelBooking(req: Request, res: Response): Promise<void> {
    try {
      const { bookingId } = req.params;
      const { cancellationReason } = req.body;

      if (!bookingId) {
        throw new ApiError(400, 'Booking ID is required');
      }

      if (!cancellationReason) {
        throw new ApiError(400, 'Cancellation reason is required');
      }

      const booking = await bookingService.cancelBooking(bookingId, cancellationReason);

      res.status(200).json({
        success: true,
        data: booking,
      });
    } catch (error: any) {
      console.error('❌ BookingController: Error cancelling booking:', error);
      throw error;
    }
  }

  /**
   * Reschedule booking
   */
  async rescheduleBooking(req: Request, res: Response): Promise<void> {
    try {
      const { bookingId } = req.params;
      const { bookingDate, startTime, staffSalonAccessId } = req.body;

      if (!bookingId) {
        throw new ApiError(400, 'Booking ID is required');
      }

      if (!bookingDate || !startTime) {
        throw new ApiError(400, 'New booking date and start time are required');
      }

      // For now, we'll implement this as a status update
      // In a full implementation, you'd want to validate the new time slot
      const booking = await bookingService.getBookingById(bookingId);

      res.status(200).json({
        success: true,
        data: booking,
        message: 'Reschedule functionality coming soon',
      });
    } catch (error: any) {
      console.error('❌ BookingController: Error rescheduling booking:', error);
      throw error;
    }
  }

  /**
   * Check slot availability
   */
  async checkSlotAvailability(req: Request, res: Response): Promise<void> {
    try {
      const { salonId, serviceId, staffId, date, startTime } = req.query;

      if (!salonId || !serviceId || !staffId || !date || !startTime) {
        throw new ApiError(400, 'All parameters are required');
      }

      // For now, return true - implement actual availability check
      const available = true;

      res.status(200).json({
        success: true,
        data: { available },
      });
    } catch (error: any) {
      console.error('❌ BookingController: Error checking slot availability:', error);
      throw error;
    }
  }

  /**
   * Get booking statistics
   */
  async getBookingStats(req: Request, res: Response): Promise<void> {
    try {
      const { salonId, dateFrom, dateTo } = req.query;

      if (!salonId) {
        throw new ApiError(400, 'Salon ID is required');
      }

      // For now, return mock stats - implement actual stats calculation
      const stats = {
        totalBookings: 0,
        pendingBookings: 0,
        confirmedBookings: 0,
        completedBookings: 0,
        cancelledBookings: 0,
        totalRevenue: 0,
        averageBookingValue: 0,
      };

      res.status(200).json({
        success: true,
        data: stats,
      });
    } catch (error: any) {
      console.error('❌ BookingController: Error getting booking stats:', error);
      throw error;
    }
  }
}

export const bookingController = new BookingController();
