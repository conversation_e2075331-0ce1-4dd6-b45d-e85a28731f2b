import { Repository } from 'typeorm';
import { AppDataSource } from '../config/typeorm';
import { 
  StaffAvailability, 
  AvailabilityType, 
  AvailabilityStatus, 
  TimeSlot 
} from '../entities/StaffAvailability';
import { StaffSalonAccess } from '../entities/StaffSalonAccess';
import { User } from '../entities/User';
import ApiError from '../utils/apiError';

export interface CreateAvailabilityRequest {
  staffSalonAccessId: string;
  type: AvailabilityType;
  date?: Date;
  dayOfWeek?: string;
  timeSlots: TimeSlot[];
  isAvailable: boolean;
  notes?: string;
  createdBy: string;
}

export interface UpdateAvailabilityRequest {
  type?: AvailabilityType;
  timeSlots?: TimeSlot[];
  isAvailable?: boolean;
  notes?: string;
  lastModifiedBy: string;
}

export interface ApproveAvailabilityRequest {
  status: AvailabilityStatus;
  adminNotes?: string;
  approvedBy: string;
}

export interface AvailabilitySearchFilters {
  staffSalonAccessId?: string;
  salonId?: string;
  type?: AvailabilityType;
  status?: AvailabilityStatus;
  date?: Date;
  dateFrom?: Date;
  dateTo?: Date;
  dayOfWeek?: string;
  isAvailable?: boolean;
  pendingApproval?: boolean;
  page?: number;
  limit?: number;
}

export interface BulkCreateAvailabilityRequest {
  staffSalonAccessId: string;
  type: AvailabilityType;
  daysOfWeek: string[];
  timeSlots: TimeSlot[];
  isAvailable: boolean;
  notes?: string;
  createdBy: string;
}

class StaffAvailabilityService {
  private availabilityRepository: Repository<StaffAvailability>;
  private staffAccessRepository: Repository<StaffSalonAccess>;
  private userRepository: Repository<User>;

  constructor() {
    this.availabilityRepository = AppDataSource.getRepository(StaffAvailability);
    this.staffAccessRepository = AppDataSource.getRepository(StaffSalonAccess);
    this.userRepository = AppDataSource.getRepository(User);
  }

  /**
   * Create a new availability record
   */
  async createAvailability(data: CreateAvailabilityRequest): Promise<StaffAvailability> {
    try {
      console.log('🔍 StaffAvailabilityService: Creating availability');

      // Validate staff access exists
      const staffAccess = await this.staffAccessRepository.findOne({
        where: { id: data.staffSalonAccessId },
        relations: ['staff', 'salon']
      });

      if (!staffAccess) {
        throw new ApiError(404, 'Staff access not found');
      }

      // Validate time slots
      this.validateTimeSlots(data.timeSlots);

      // Check for conflicts
      await this.checkForConflicts(data);

      // Create availability
      const availability = this.availabilityRepository.create({
        ...data,
        createdAt: new Date(),
      });

      const savedAvailability = await this.availabilityRepository.save(availability);

      console.log('✅ StaffAvailabilityService: Availability created successfully');
      return savedAvailability;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error creating availability:', error);
      throw error;
    }
  }

  /**
   * Get availability records with filters
   */
  async getAvailability(filters: AvailabilitySearchFilters = {}): Promise<{
    data: StaffAvailability[];
    total: number;
    page: number;
    totalPages: number;
  }> {
    try {
      console.log('🔍 StaffAvailabilityService: Getting availability with filters:', filters);

      const queryBuilder = this.availabilityRepository
        .createQueryBuilder('availability')
        .leftJoinAndSelect('availability.staffSalonAccess', 'staffAccess')
        .leftJoinAndSelect('staffAccess.staff', 'staff')
        .leftJoinAndSelect('staffAccess.salon', 'salon')
        .leftJoinAndSelect('availability.creator', 'creator')
        .leftJoinAndSelect('availability.approver', 'approver');

      // Apply filters
      if (filters.staffSalonAccessId) {
        queryBuilder.andWhere('availability.staffSalonAccessId = :staffSalonAccessId', {
          staffSalonAccessId: filters.staffSalonAccessId
        });
      }

      if (filters.salonId) {
        queryBuilder.andWhere('staffAccess.salonId = :salonId', {
          salonId: filters.salonId
        });
      }

      if (filters.type) {
        queryBuilder.andWhere('availability.type = :type', {
          type: filters.type
        });
      }

      if (filters.status) {
        queryBuilder.andWhere('availability.status = :status', {
          status: filters.status
        });
      }

      if (filters.date) {
        queryBuilder.andWhere('availability.date = :date', {
          date: filters.date
        });
      }

      if (filters.dateFrom && filters.dateTo) {
        queryBuilder.andWhere('availability.date BETWEEN :dateFrom AND :dateTo', {
          dateFrom: filters.dateFrom,
          dateTo: filters.dateTo
        });
      }

      if (filters.dayOfWeek) {
        queryBuilder.andWhere('availability.dayOfWeek = :dayOfWeek', {
          dayOfWeek: filters.dayOfWeek
        });
      }

      if (filters.isAvailable !== undefined) {
        queryBuilder.andWhere('availability.isAvailable = :isAvailable', {
          isAvailable: filters.isAvailable
        });
      }

      if (filters.pendingApproval) {
        queryBuilder.andWhere('availability.status = :pendingStatus', {
          pendingStatus: AvailabilityStatus.PENDING
        });
      }

      // Apply sorting
      queryBuilder.orderBy('availability.date', 'ASC')
                  .addOrderBy('availability.dayOfWeek', 'ASC')
                  .addOrderBy('availability.createdAt', 'DESC');

      // Apply pagination
      const page = filters.page || 1;
      const limit = filters.limit || 20;
      const offset = (page - 1) * limit;

      const [availability, total] = await queryBuilder
        .skip(offset)
        .take(limit)
        .getManyAndCount();

      const totalPages = Math.ceil(total / limit);

      console.log('✅ StaffAvailabilityService: Retrieved availability:', availability.length);
      return {
        data: availability,
        total,
        page,
        totalPages,
      };
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error getting availability:', error);
      throw error;
    }
  }

  /**
   * Get availability by ID
   */
  async getAvailabilityById(availabilityId: string): Promise<StaffAvailability> {
    try {
      const availability = await this.availabilityRepository.findOne({
        where: { id: availabilityId },
        relations: ['staffSalonAccess', 'staffSalonAccess.staff', 'staffSalonAccess.salon', 'creator', 'approver']
      });

      if (!availability) {
        throw new ApiError(404, 'Availability not found');
      }

      return availability;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error getting availability:', error);
      throw error;
    }
  }

  /**
   * Update availability
   */
  async updateAvailability(availabilityId: string, data: UpdateAvailabilityRequest): Promise<StaffAvailability> {
    try {
      const availability = await this.getAvailabilityById(availabilityId);

      if (data.timeSlots) {
        this.validateTimeSlots(data.timeSlots);
      }

      Object.assign(availability, {
        ...data,
        lastModifiedAt: new Date(),
      });

      const updatedAvailability = await this.availabilityRepository.save(availability);

      console.log('✅ StaffAvailabilityService: Availability updated successfully');
      return updatedAvailability;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error updating availability:', error);
      throw error;
    }
  }

  /**
   * Approve or reject availability
   */
  async approveAvailability(availabilityId: string, data: ApproveAvailabilityRequest): Promise<StaffAvailability> {
    try {
      const availability = await this.getAvailabilityById(availabilityId);

      Object.assign(availability, {
        status: data.status,
        adminNotes: data.adminNotes,
        approvedBy: data.approvedBy,
        approvedAt: new Date(),
      });

      const updatedAvailability = await this.availabilityRepository.save(availability);

      console.log('✅ StaffAvailabilityService: Availability approval updated');
      return updatedAvailability;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error approving availability:', error);
      throw error;
    }
  }

  /**
   * Delete availability
   */
  async deleteAvailability(availabilityId: string): Promise<void> {
    try {
      const availability = await this.getAvailabilityById(availabilityId);
      await this.availabilityRepository.remove(availability);

      console.log('✅ StaffAvailabilityService: Availability deleted successfully');
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error deleting availability:', error);
      throw error;
    }
  }

  /**
   * Bulk create availability for multiple days
   */
  async bulkCreateAvailability(data: BulkCreateAvailabilityRequest): Promise<StaffAvailability[]> {
    try {
      console.log('🔍 StaffAvailabilityService: Bulk creating availability');

      const availabilities: StaffAvailability[] = [];

      for (const dayOfWeek of data.daysOfWeek) {
        try {
          const availability = await this.createAvailability({
            staffSalonAccessId: data.staffSalonAccessId,
            type: data.type,
            dayOfWeek,
            timeSlots: data.timeSlots,
            isAvailable: data.isAvailable,
            notes: data.notes,
            createdBy: data.createdBy,
          });
          availabilities.push(availability);
        } catch (error: any) {
          // Continue with other days if one fails
          console.warn('⚠️ Failed to create availability for day:', dayOfWeek, error.message);
        }
      }

      console.log('✅ StaffAvailabilityService: Bulk availability created:', availabilities.length);
      return availabilities;
    } catch (error: any) {
      console.error('❌ StaffAvailabilityService: Error in bulk creation:', error);
      throw error;
    }
  }

  /**
   * Validate time slots
   */
  private validateTimeSlots(timeSlots: TimeSlot[]): void {
    for (const slot of timeSlots) {
      // Validate time format
      if (!/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(slot.startTime) ||
          !/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/.test(slot.endTime)) {
        throw new ApiError(400, 'Invalid time format. Use HH:mm format');
      }

      // Validate start time is before end time
      const start = new Date(`2000-01-01T${slot.startTime}:00`);
      const end = new Date(`2000-01-01T${slot.endTime}:00`);
      
      if (start >= end) {
        throw new ApiError(400, 'Start time must be before end time');
      }
    }

    // Check for overlapping slots
    for (let i = 0; i < timeSlots.length; i++) {
      for (let j = i + 1; j < timeSlots.length; j++) {
        const slot1 = timeSlots[i];
        const slot2 = timeSlots[j];
        
        const start1 = new Date(`2000-01-01T${slot1.startTime}:00`);
        const end1 = new Date(`2000-01-01T${slot1.endTime}:00`);
        const start2 = new Date(`2000-01-01T${slot2.startTime}:00`);
        const end2 = new Date(`2000-01-01T${slot2.endTime}:00`);
        
        if ((start1 < end2 && end1 > start2)) {
          throw new ApiError(400, 'Time slots cannot overlap');
        }
      }
    }
  }

  /**
   * Check for conflicts with existing availability
   */
  private async checkForConflicts(data: CreateAvailabilityRequest): Promise<void> {
    const existingQuery = this.availabilityRepository
      .createQueryBuilder('availability')
      .where('availability.staffSalonAccessId = :staffSalonAccessId', {
        staffSalonAccessId: data.staffSalonAccessId
      })
      .andWhere('availability.status = :status', {
        status: AvailabilityStatus.ACTIVE
      });

    if (data.date) {
      existingQuery.andWhere('availability.date = :date', { date: data.date });
    } else if (data.dayOfWeek) {
      existingQuery.andWhere('availability.dayOfWeek = :dayOfWeek', { dayOfWeek: data.dayOfWeek });
    }

    const existing = await existingQuery.getOne();

    if (existing && data.type === AvailabilityType.REGULAR) {
      throw new ApiError(400, 'Regular availability already exists for this day');
    }
  }
}

export const staffAvailabilityService = new StaffAvailabilityService();
