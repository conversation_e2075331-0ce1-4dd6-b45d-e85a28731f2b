-- Create staff_service_assignments table manually
-- This script creates the table if it doesn't exist

-- Create the table
CREATE TABLE IF NOT EXISTS "staff_service_assignments" (
  "id" uuid NOT NULL DEFAULT uuid_generate_v4(),
  "staffSalonAccessId" uuid NOT NULL,
  "serviceId" uuid NOT NULL,
  "status" character varying NOT NULL DEFAULT 'ACTIVE',
  "specializationLevel" character varying NOT NULL DEFAULT 'INTERMEDIATE',
  "commissionRate" numeric(5,2),
  "customRate" numeric(10,2),
  "isBookable" boolean NOT NULL DEFAULT true,
  "priority" integer,
  "notes" text,
  "startDate" TIMESTAMP NOT NULL,
  "endDate" TIMESTAMP,
  "assignedBy" uuid NOT NULL,
  "assignedAt" TIMESTAMP NOT NULL,
  "lastModifiedBy" uuid,
  "lastModifiedAt" TIMESTAMP,
  "createdAt" TIMESTAMP NOT NULL DEFAULT now(),
  "updatedAt" TIMESTAMP NOT NULL DEFAULT now(),
  CONSTRAINT "PK_staff_service_assignments" PRIMARY KEY ("id")
);

-- Add unique constraint for staff-service combination (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'UQ_staff_service_assignment'
  ) THEN
    ALTER TABLE "staff_service_assignments" 
    ADD CONSTRAINT "UQ_staff_service_assignment" 
    UNIQUE ("staffSalonAccessId", "serviceId");
  END IF;
END $$;

-- Add indexes for better performance (if not exists)
CREATE INDEX IF NOT EXISTS "IDX_staff_service_assignment_staff" 
ON "staff_service_assignments" ("staffSalonAccessId");

CREATE INDEX IF NOT EXISTS "IDX_staff_service_assignment_service" 
ON "staff_service_assignments" ("serviceId");

CREATE INDEX IF NOT EXISTS "IDX_staff_service_assignment_status" 
ON "staff_service_assignments" ("status");

CREATE INDEX IF NOT EXISTS "IDX_staff_service_assignment_assigned_by" 
ON "staff_service_assignments" ("assignedBy");

-- Add foreign key constraints (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'FK_staff_service_assignment_staff_access'
  ) THEN
    ALTER TABLE "staff_service_assignments" 
    ADD CONSTRAINT "FK_staff_service_assignment_staff_access" 
    FOREIGN KEY ("staffSalonAccessId") 
    REFERENCES "staff_salon_access"("id") 
    ON DELETE CASCADE ON UPDATE NO ACTION;
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'FK_staff_service_assignment_service'
  ) THEN
    ALTER TABLE "staff_service_assignments" 
    ADD CONSTRAINT "FK_staff_service_assignment_service" 
    FOREIGN KEY ("serviceId") 
    REFERENCES "services"("id") 
    ON DELETE CASCADE ON UPDATE NO ACTION;
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'FK_staff_service_assignment_assigned_by'
  ) THEN
    ALTER TABLE "staff_service_assignments" 
    ADD CONSTRAINT "FK_staff_service_assignment_assigned_by" 
    FOREIGN KEY ("assignedBy") 
    REFERENCES "users"("id") 
    ON DELETE SET NULL ON UPDATE NO ACTION;
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'FK_staff_service_assignment_last_modified_by'
  ) THEN
    ALTER TABLE "staff_service_assignments" 
    ADD CONSTRAINT "FK_staff_service_assignment_last_modified_by" 
    FOREIGN KEY ("lastModifiedBy") 
    REFERENCES "users"("id") 
    ON DELETE SET NULL ON UPDATE NO ACTION;
  END IF;
END $$;

-- Add check constraints for valid enum values (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'CHK_staff_service_assignment_status'
  ) THEN
    ALTER TABLE "staff_service_assignments" 
    ADD CONSTRAINT "CHK_staff_service_assignment_status" 
    CHECK ("status" IN ('ACTIVE', 'INACTIVE', 'SUSPENDED'));
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'CHK_staff_service_assignment_specialization'
  ) THEN
    ALTER TABLE "staff_service_assignments" 
    ADD CONSTRAINT "CHK_staff_service_assignment_specialization" 
    CHECK ("specializationLevel" IN ('BEGINNER', 'INTERMEDIATE', 'ADVANCED', 'EXPERT', 'MASTER'));
  END IF;
END $$;

-- Add check constraints for valid ranges (if not exists)
DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'CHK_staff_service_assignment_commission_rate'
  ) THEN
    ALTER TABLE "staff_service_assignments" 
    ADD CONSTRAINT "CHK_staff_service_assignment_commission_rate" 
    CHECK ("commissionRate" IS NULL OR ("commissionRate" >= 0 AND "commissionRate" <= 100));
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'CHK_staff_service_assignment_custom_rate'
  ) THEN
    ALTER TABLE "staff_service_assignments" 
    ADD CONSTRAINT "CHK_staff_service_assignment_custom_rate" 
    CHECK ("customRate" IS NULL OR "customRate" >= 0);
  END IF;
END $$;

DO $$ 
BEGIN
  IF NOT EXISTS (
    SELECT 1 FROM pg_constraint 
    WHERE conname = 'CHK_staff_service_assignment_priority'
  ) THEN
    ALTER TABLE "staff_service_assignments" 
    ADD CONSTRAINT "CHK_staff_service_assignment_priority" 
    CHECK ("priority" IS NULL OR "priority" >= 0);
  END IF;
END $$;

-- Success message
SELECT 'Staff service assignments table created successfully!' as result;
