export enum BookingStatus {
  PENDING = 'PENDING',
  CONFIRMED = 'CONFIRMED',
  IN_PROGRESS = 'IN_PROGRESS',
  COMPLETED = 'COMPLETED',
  CANCELLED = 'CANCELLED',
  NO_SHOW = 'NO_SHOW',
}

export enum PaymentStatus {
  PENDING = 'PENDING',
  PAID = 'PAID',
  PARTIALLY_PAID = 'PARTIALLY_PAID',
  REFUNDED = 'REFUNDED',
  FAILED = 'FAILED',
}

export interface TimeSlot {
  startTime: string; // Format: "HH:mm"
  endTime: string;   // Format: "HH:mm"
  isAvailable: boolean;
  staffId?: string;
  staffName?: string;
}

export interface AvailableStaff {
  staffSalonAccessId: string;
  staffId: string;
  firstName: string;
  lastName: string;
  profileImage?: string;
  position: string;
  rating?: number;
  reviewCount?: number;
  specializationLevel: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED' | 'EXPERT';
  availableSlots: TimeSlot[];
}

export interface BookingService {
  serviceId: string;
  name: string;
  category: string;
  duration: number; // in minutes
  price: number;
  image?: string;
}

export interface CreateBookingRequest {
  salonId: string;
  customerId: string;
  serviceId: string;
  staffSalonAccessId: string;
  bookingDate: string; // Format: "YYYY-MM-DD"
  startTime: string;   // Format: "HH:mm"
  endTime: string;     // Format: "HH:mm"
  notes?: string;
  specialRequests?: string;
  offerId?: string; // If applying an offer
}

export interface BookingResponse {
  id: string;
  bookingNumber: string;
  status: BookingStatus;
  paymentStatus: PaymentStatus;
  
  // Service details
  service: BookingService;
  
  // Staff details
  staff: {
    staffSalonAccessId: string;
    firstName: string;
    lastName: string;
    profileImage?: string;
    position: string;
  };
  
  // Salon details
  salon: {
    id: string;
    name: string;
    address: string;
    phone: string;
    image?: string;
  };
  
  // Customer details
  customer: {
    id: string;
    firstName: string;
    lastName: string;
    email: string;
    phone?: string;
  };
  
  // Booking details
  bookingDate: string;
  startTime: string;
  endTime: string;
  duration: number;
  
  // Pricing
  originalPrice: number;
  discountAmount: number;
  finalPrice: number;
  
  // Additional info
  notes?: string;
  specialRequests?: string;
  cancellationReason?: string;
  
  // Applied offer
  appliedOffer?: {
    id: string;
    title: string;
    discountType: string;
    discountValue: number;
  };
  
  // Timestamps
  createdAt: string;
  updatedAt: string;
  confirmedAt?: string;
  completedAt?: string;
  cancelledAt?: string;
}

export interface BookingFilters {
  status?: BookingStatus;
  paymentStatus?: PaymentStatus;
  dateFrom?: string;
  dateTo?: string;
  serviceId?: string;
  staffId?: string;
  customerId?: string;
  search?: string;
  sortBy?: 'bookingDate' | 'createdAt' | 'finalPrice' | 'status';
  sortOrder?: 'ASC' | 'DESC';
  limit?: number;
  offset?: number;
}

export interface BookingListResponse {
  data: BookingResponse[];
  total: number;
  page: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

export interface AvailabilityRequest {
  salonId: string;
  serviceId: string;
  date: string; // Format: "YYYY-MM-DD"
  preferredStaffId?: string;
}

export interface AvailabilityResponse {
  date: string;
  availableStaff: AvailableStaff[];
  totalSlots: number;
  availableSlots: number;
  busySlots: number;
}

export interface BookingStats {
  totalBookings: number;
  pendingBookings: number;
  confirmedBookings: number;
  completedBookings: number;
  cancelledBookings: number;
  totalRevenue: number;
  averageBookingValue: number;
}

// Utility functions
export const formatBookingTime = (startTime: string, endTime: string): string => {
  const formatTime = (time: string) => {
    const [hours, minutes] = time.split(':');
    const hour = parseInt(hours);
    const ampm = hour >= 12 ? 'PM' : 'AM';
    const displayHour = hour % 12 || 12;
    return `${displayHour}:${minutes} ${ampm}`;
  };
  
  return `${formatTime(startTime)} - ${formatTime(endTime)}`;
};

export const getBookingStatusColor = (status: BookingStatus): string => {
  switch (status) {
    case BookingStatus.PENDING:
      return '#FFA500'; // Orange
    case BookingStatus.CONFIRMED:
      return '#007BFF'; // Blue
    case BookingStatus.IN_PROGRESS:
      return '#17A2B8'; // Teal
    case BookingStatus.COMPLETED:
      return '#28A745'; // Green
    case BookingStatus.CANCELLED:
      return '#DC3545'; // Red
    case BookingStatus.NO_SHOW:
      return '#6C757D'; // Gray
    default:
      return '#6C757D';
  }
};

export const getBookingStatusLabel = (status: BookingStatus): string => {
  switch (status) {
    case BookingStatus.PENDING:
      return 'Pending';
    case BookingStatus.CONFIRMED:
      return 'Confirmed';
    case BookingStatus.IN_PROGRESS:
      return 'In Progress';
    case BookingStatus.COMPLETED:
      return 'Completed';
    case BookingStatus.CANCELLED:
      return 'Cancelled';
    case BookingStatus.NO_SHOW:
      return 'No Show';
    default:
      return 'Unknown';
  }
};

export const getPaymentStatusColor = (status: PaymentStatus): string => {
  switch (status) {
    case PaymentStatus.PENDING:
      return '#FFA500'; // Orange
    case PaymentStatus.PAID:
      return '#28A745'; // Green
    case PaymentStatus.PARTIALLY_PAID:
      return '#FFC107'; // Yellow
    case PaymentStatus.REFUNDED:
      return '#17A2B8'; // Teal
    case PaymentStatus.FAILED:
      return '#DC3545'; // Red
    default:
      return '#6C757D';
  }
};

export const getPaymentStatusLabel = (status: PaymentStatus): string => {
  switch (status) {
    case PaymentStatus.PENDING:
      return 'Pending';
    case PaymentStatus.PAID:
      return 'Paid';
    case PaymentStatus.PARTIALLY_PAID:
      return 'Partially Paid';
    case PaymentStatus.REFUNDED:
      return 'Refunded';
    case PaymentStatus.FAILED:
      return 'Failed';
    default:
      return 'Unknown';
  }
};
