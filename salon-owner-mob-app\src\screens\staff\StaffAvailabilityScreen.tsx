import React, { useState, useEffect } from 'react';
import {
  View,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  Alert,
  Dimensions,
  RefreshControl,
} from 'react-native';
import { RouteProp, useRoute, useNavigation } from '@react-navigation/native';
import { StackNavigationProp } from '@react-navigation/stack';
import Icon from 'react-native-vector-icons/MaterialIcons';

import { DarkTitle, DarkBody } from '../../components/common/Typography';
import { Button } from '../../components/common/Button';
import { Colors } from '../../constants/colors';
import { useToast } from '../../context/ToastContext';
import { useSalon } from '../../context/SalonContext';
import { BusinessHours, DayHours, DAYS_OF_WEEK as BUSINESS_DAYS, formatTime } from '../../types/businessHours';
import { staffAvailabilityService } from '../../services/staffAvailabilityService';
import {
  StaffAvailability,
  AvailabilityType,
  AvailabilityStatus,
  TimeSlot,
  getAvailabilityTypeLabel,
  getAvailabilityTypeColor,
  getAvailabilityStatusLabel,
  getAvailabilityStatusColor,
  formatTimeSlots,
  generateTimeSlots,
  DAYS_OF_WEEK,
  DAY_LABELS,
} from '../../types/staffAvailability';
import { SafeAreaView } from 'react-native-safe-area-context';

const { width, height } = Dimensions.get('window');

type RootStackParamList = {
  StaffAvailability: {
    staffId: string;
    staffName: string;
    staffSalonAccessId: string;
  };
  StaffTimeline: {
    availability: StaffAvailability;
    staffName: string;
  };
};

type StaffAvailabilityScreenRouteProp = RouteProp<RootStackParamList, 'StaffAvailability'>;
type StaffAvailabilityScreenNavigationProp = StackNavigationProp<RootStackParamList>;

export default function StaffAvailabilityScreen() {
  const route = useRoute<StaffAvailabilityScreenRouteProp>();
  const navigation = useNavigation<StaffAvailabilityScreenNavigationProp>();
  const { showToast } = useToast();
  const { selectedSalon } = useSalon();

  const { staffId, staffName, staffSalonAccessId } = route.params;

  const [loading, setLoading] = useState(true);
  const [refreshing, setRefreshing] = useState(false);
  const [availabilities, setAvailabilities] = useState<StaffAvailability[]>([]);
  const [selectedTab, setSelectedTab] = useState<'regular' | 'overrides' | 'pending'>('regular');

  // Function to format salon business hours for display
  const formatSalonBusinessHours = (businessHours: BusinessHours): string => {
    const openDays = BUSINESS_DAYS.filter(day => businessHours[day]?.isOpen);

    if (openDays.length === 0) {
      return 'Closed all days';
    }

    // Check if all open days have the same hours
    const firstDay = businessHours[openDays[0]];
    const allSameHours = openDays.every(day => {
      const dayHours = businessHours[day];
      return dayHours.openTime === firstDay.openTime && dayHours.closeTime === firstDay.closeTime;
    });

    if (allSameHours && openDays.length === 7) {
      return `Daily ${formatTime(firstDay.openTime!)} - ${formatTime(firstDay.closeTime!)}`;
    } else if (allSameHours) {
      const dayNames = openDays.map(day => day.charAt(0).toUpperCase() + day.slice(1, 3)).join(', ');
      return `${dayNames}: ${formatTime(firstDay.openTime!)} - ${formatTime(firstDay.closeTime!)}`;
    } else {
      // Show first few days as example
      const examples = openDays.slice(0, 2).map(day => {
        const dayHours = businessHours[day];
        return `${day.charAt(0).toUpperCase() + day.slice(1, 3)}: ${formatTime(dayHours.openTime!)} - ${formatTime(dayHours.closeTime!)}`;
      });
      return examples.join(', ') + (openDays.length > 2 ? '...' : '');
    }
  };

  useEffect(() => {
    loadData();
  }, [selectedTab]);

  const loadData = async () => {
    try {
      setLoading(true);
      await loadAvailabilities();
    } catch (error: any) {
      console.error('❌ StaffAvailabilityScreen: Error loading data:', error);
      showToast('Failed to load availability data', 'error');
    } finally {
      setLoading(false);
    }
  };

  // Function to convert salon business hours to staff time slots
  const convertBusinessHoursToTimeSlots = (businessHours: BusinessHours): StaffAvailability[] => {
    const availabilities: StaffAvailability[] = [];

    BUSINESS_DAYS.forEach((day) => {
      const dayHours = businessHours[day];
      if (dayHours && dayHours.isOpen && dayHours.openTime && dayHours.closeTime) {
        let timeSlots: TimeSlot[] = [];

        if (dayHours.isBreakTime && dayHours.breakStartTime && dayHours.breakEndTime) {
          // Split into morning, break, and afternoon slots
          const morningSlots = generateTimeSlots(dayHours.openTime, dayHours.breakStartTime, 30);
          const breakSlots = generateTimeSlots(dayHours.breakStartTime, dayHours.breakEndTime, 30).map(slot => ({
            ...slot,
            isAvailable: false, // Mark break slots as unavailable
            notes: 'Break time'
          }));
          const afternoonSlots = generateTimeSlots(dayHours.breakEndTime, dayHours.closeTime, 30);
          timeSlots = [...morningSlots, ...breakSlots, ...afternoonSlots];
        } else {
          // Single continuous slot
          timeSlots = generateTimeSlots(dayHours.openTime, dayHours.closeTime, 30);
        }

        const totalHours = timeSlots.reduce((total, slot) => {
          if (slot.isAvailable) {
            const start = new Date(`2000-01-01T${slot.startTime}:00`);
            const end = new Date(`2000-01-01T${slot.endTime}:00`);
            return total + (end.getTime() - start.getTime()) / (1000 * 60 * 60);
          }
          return total;
        }, 0);

        availabilities.push({
          id: `default-${day}`,
          staffSalonAccessId,
          type: AvailabilityType.REGULAR,
          status: AvailabilityStatus.ACTIVE,
          dayOfWeek: day,
          timeSlots,
          isAvailable: true,
          createdBy: 'system',
          createdAt: new Date(),
          systemCreatedAt: new Date(),
          systemUpdatedAt: new Date(),
          isRegularSchedule: true,
          isOverride: false,
          isPendingApproval: false,
          isApproved: true,
          totalAvailableHours: totalHours,
          displayDate: DAY_LABELS[day] || day.charAt(0).toUpperCase() + day.slice(1),
          displayStatus: 'Active',
          displayType: 'Regular Schedule',
          notes: 'Generated from salon business hours',
        });
      }
    });

    return availabilities;
  };

  const loadAvailabilities = async () => {
    try {
      // Load real availability data from API
      const filters: any = {
        staffSalonAccessId,
        status: AvailabilityStatus.ACTIVE,
        limit: 50, // Load more records for better UX
      };

      // Add type filter based on selected tab
      switch (selectedTab) {
        case 'regular':
          filters.type = AvailabilityType.REGULAR;
          break;
        case 'overrides':
          // For overrides tab, we'll filter after getting results
          break;
        case 'pending':
          filters.status = AvailabilityStatus.PENDING;
          break;
      }

      const result = await staffAvailabilityService.getAvailability(filters);
      let loadedAvailabilities = result.data;

      // If no availability records found, generate from salon business hours
      if (loadedAvailabilities.length === 0 && selectedSalon?.businessHours && selectedTab === 'regular') {
        console.log('🔄 StaffAvailabilityScreen: No availability found, generating from salon business hours');
        loadedAvailabilities = convertBusinessHoursToTimeSlots(selectedSalon.businessHours);

        // TODO: Optionally save these to the database
        // for (const availability of loadedAvailabilities) {
        //   await staffAvailabilityService.createAvailability({
        //     staffSalonAccessId: availability.staffSalonAccessId,
        //     type: availability.type,
        //     dayOfWeek: availability.dayOfWeek,
        //     timeSlots: availability.timeSlots,
        //     isAvailable: availability.isAvailable,
        //     notes: availability.notes,
        //   });
        // }
      }

      // Filter for overrides tab (OVERRIDE and SPECIAL types)
      if (selectedTab === 'overrides') {
        loadedAvailabilities = loadedAvailabilities.filter(availability =>
          availability.type === AvailabilityType.OVERRIDE ||
          availability.type === AvailabilityType.SPECIAL
        );
      }

      setAvailabilities(loadedAvailabilities);
      console.log('✅ StaffAvailabilityScreen: Loaded availabilities:', loadedAvailabilities.length);
    } catch (error: any) {
      console.error('❌ StaffAvailabilityScreen: Error loading availabilities:', error);

      // Fallback to generated data if API fails
      if (selectedSalon?.businessHours && selectedTab === 'regular') {
        console.log('🔄 StaffAvailabilityScreen: API failed, using salon business hours as fallback');
        const fallbackAvailabilities = convertBusinessHoursToTimeSlots(selectedSalon.businessHours);
        setAvailabilities(fallbackAvailabilities);
      } else {
        throw error;
      }
    }
  };

  const handleRefresh = async () => {
    setRefreshing(true);
    await loadData();
    setRefreshing(false);
  };

  const handleAddAvailability = () => {
    // TODO: Navigate to AddAvailabilityScreen
    showToast('Add availability feature coming soon', 'info');
  };

  const handleInitializeFromSalonHours = () => {
    if (!selectedSalon?.businessHours) {
      showToast('No salon business hours found', 'error');
      return;
    }

    Alert.alert(
      'Initialize Schedule',
      'This will create a regular schedule based on salon business hours. Any existing regular schedule will be replaced. Continue?',
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Initialize',
          onPress: async () => {
            try {
              // TODO: Implement actual initialization
              const newAvailabilities = convertBusinessHoursToTimeSlots(selectedSalon.businessHours!);
              setAvailabilities(prev => [
                ...prev.filter(a => a.type !== AvailabilityType.REGULAR),
                ...newAvailabilities
              ]);
              showToast('Schedule initialized from salon business hours', 'success');
            } catch (error: any) {
              console.error('❌ StaffAvailabilityScreen: Error initializing schedule:', error);
              showToast('Failed to initialize schedule', 'error');
            }
          },
        },
      ]
    );
  };

  const handleEditAvailability = (availability: StaffAvailability) => {
    // TODO: Navigate to EditAvailabilityScreen
    showToast('Edit availability feature coming soon', 'info');
  };

  // Example function to block specific date and time
  const blockSpecificDateTime = async () => {
    try {
      // Example: Block June 20, 2025 from 11:00-11:45
      const targetDate = new Date('2025-06-20');
      const blockedSlots = [
        { startTime: '11:00', endTime: '11:15' },
        { startTime: '11:15', endTime: '11:30' },
        { startTime: '11:30', endTime: '11:45' }
      ];

      const result = await staffAvailabilityService.blockTimeSlots(
        staffSalonAccessId,
        targetDate,
        blockedSlots,
        'Blocked 11:00-11:45 on June 20, 2025 by admin'
      );

      showToast('Successfully blocked June 20, 2025 from 11:00-11:45', 'success');
      console.log('✅ Blocked time slots:', result);

      // Refresh the availability list
      await loadData();
    } catch (error: any) {
      console.error('❌ Error blocking specific date/time:', error);
      showToast('Failed to block time slots', 'error');
    }
  };

  const handleDeleteAvailability = (availability: StaffAvailability) => {
    Alert.alert(
      'Delete Availability',
      `Are you sure you want to delete this ${availability.displayType.toLowerCase()}?`,
      [
        { text: 'Cancel', style: 'cancel' },
        {
          text: 'Delete',
          style: 'destructive',
          onPress: async () => {
            try {
              // TODO: Implement delete functionality
              showToast('Availability deleted successfully', 'success');
              await loadData();
            } catch (error: any) {
              console.error('❌ StaffAvailabilityScreen: Error deleting availability:', error);
              showToast('Failed to delete availability', 'error');
            }
          },
        },
      ]
    );
  };

  const renderTabButton = (tab: 'regular' | 'overrides' | 'pending', label: string, count: number) => (
    <TouchableOpacity
      style={[styles.tabButton, selectedTab === tab && styles.activeTabButton]}
      onPress={() => setSelectedTab(tab)}
    >
      <DarkBody size="medium" style={[
        styles.tabButtonText,
        selectedTab === tab && styles.activeTabButtonText
      ]}>
        {label} ({count})
      </DarkBody>
    </TouchableOpacity>
  );

  const getWorkingHours = (availability: StaffAvailability): { startTime: string; endTime: string } => {
    if (!availability.timeSlots || availability.timeSlots.length === 0) {
      return { startTime: 'Not set', endTime: 'Not set' };
    }

    const availableSlots = availability.timeSlots.filter(slot => slot.isAvailable);
    if (availableSlots.length === 0) {
      return { startTime: 'Not available', endTime: 'Not available' };
    }

    // Find earliest start time and latest end time
    let earliestStart = availableSlots[0].startTime;
    let latestEnd = availableSlots[0].endTime;

    availableSlots.forEach(slot => {
      if (slot.startTime < earliestStart) earliestStart = slot.startTime;
      if (slot.endTime > latestEnd) latestEnd = slot.endTime;
    });

    return {
      startTime: formatTime(earliestStart),
      endTime: formatTime(latestEnd)
    };
  };

  const handleViewTimeline = (availability: StaffAvailability) => {
    console.log('📊 StaffAvailabilityScreen: Navigating to timeline for:', availability.displayDate);
    navigation.navigate('StaffTimeline', {
      availability,
      staffName,
    });
  };

  const renderAvailabilityCard = (availability: StaffAvailability) => {
    const workingHours = getWorkingHours(availability);

    return (
      <TouchableOpacity
        key={availability.id}
        style={styles.availabilityCard}
        onPress={() => handleViewTimeline(availability)}
        activeOpacity={0.7}
      >
        {/* Header */}
        <View style={styles.cardHeader}>
          <View style={styles.cardHeaderLeft}>
            <View style={[
              styles.typeBadge,
              { backgroundColor: getAvailabilityTypeColor(availability.type) + '20' }
            ]}>
              <DarkBody size="small" style={[
                styles.typeBadgeText,
                { color: getAvailabilityTypeColor(availability.type) }
              ]}>
                {getAvailabilityTypeLabel(availability.type)}
              </DarkBody>
            </View>

            <View style={[
              styles.statusBadge,
              { backgroundColor: getAvailabilityStatusColor(availability.status) + '20' }
            ]}>
              <DarkBody size="small" style={[
                styles.statusBadgeText,
                { color: getAvailabilityStatusColor(availability.status) }
              ]}>
                {getAvailabilityStatusLabel(availability.status)}
              </DarkBody>
            </View>
          </View>

          <View style={styles.cardActions}>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={(e) => {
                e.stopPropagation();
                handleEditAvailability(availability);
              }}
            >
              <Icon name="edit" size={20} color={Colors.primary} />
            </TouchableOpacity>
            <TouchableOpacity
              style={styles.actionButton}
              onPress={(e) => {
                e.stopPropagation();
                handleDeleteAvailability(availability);
              }}
            >
              <Icon name="delete" size={20} color={Colors.error} />
            </TouchableOpacity>
          </View>
        </View>

        {/* Content */}
        <View style={styles.cardContent}>
          <View style={styles.dayAndHours}>
            <DarkTitle level={4} style={styles.cardTitle}>
              {availability.displayDate}
            </DarkTitle>

            <View style={styles.workingHoursContainer}>
              <View style={styles.timeDisplay}>
                <Icon name="schedule" size={16} color={Colors.primary} />
                <DarkBody size="medium" style={styles.workingHours}>
                  {workingHours.startTime} - {workingHours.endTime}
                </DarkBody>
              </View>

              <DarkBody size="small" style={styles.totalHours}>
                {availability.totalAvailableHours.toFixed(1)}h total
              </DarkBody>
            </View>
          </View>

          {/* Timeline Preview */}
          <View style={styles.timelinePreview}>
            <DarkBody size="small" style={styles.timelineLabel}>
              Tap to view detailed timeline
            </DarkBody>
            <Icon name="timeline" size={16} color={Colors.gray400} />
          </View>

          {availability.notes === 'Generated from salon business hours' && (
            <View style={styles.salonIndicator}>
              <Icon name="business" size={12} color={Colors.success} />
              <DarkBody size="small" style={styles.salonIndicatorText}>
                Based on salon hours
              </DarkBody>
            </View>
          )}
        </View>
      </TouchableOpacity>
    );
  };

  if (loading) {
    return (
      <View style={styles.container}>
        <View style={styles.header}>
          <TouchableOpacity onPress={() => navigation.goBack()}>
            <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
          </TouchableOpacity>
          <DarkTitle level={3} style={styles.headerTitle}>
            Staff Availability
          </DarkTitle>
          <View style={{ width: 24 }} />
        </View>
        <View style={styles.loadingContainer}>
          <DarkBody>Loading availability...</DarkBody>
        </View>
      </View>
    );
  }

  return ( 
    <SafeAreaView style={styles.container}>
      {/* Header */}
      <View style={styles.header}>
        <TouchableOpacity onPress={() => navigation.goBack()}>
          <Icon name="arrow-back" size={24} color={Colors.textPrimary} />
        </TouchableOpacity>
        <DarkTitle level={3} style={styles.headerTitle}>
          Staff Availability
        </DarkTitle>
        <TouchableOpacity onPress={handleAddAvailability}>
          <Icon name="add" size={24} color={Colors.primary} />
        </TouchableOpacity>
      </View>

      {/* Staff Info */}
      <View style={styles.staffInfo}>
        <DarkTitle level={4}>{staffName}</DarkTitle>
        <DarkBody size="small" style={styles.staffSubtitle}>
          Manage working hours and time slots
        </DarkBody>

        {/* Salon Business Hours Reference */}
        {selectedSalon?.businessHours && (
          <View style={styles.salonHoursReference}>
            <View style={styles.salonHoursHeader}>
              <DarkBody size="small" style={styles.salonHoursTitle}>
                Salon Business Hours:
              </DarkBody>
              <TouchableOpacity
                style={styles.initializeButton}
                onPress={handleInitializeFromSalonHours}
              >
                <Icon name="schedule" size={16} color={Colors.primary} />
                <DarkBody size="small" style={styles.initializeButtonText}>
                  Use Salon Hours
                </DarkBody>
              </TouchableOpacity>
            </View>
            <DarkBody size="small" style={styles.salonHoursText}>
              {formatSalonBusinessHours(selectedSalon.businessHours)}
            </DarkBody>
          </View>
        )}
      </View>

      {/* Tabs */}
      <View style={styles.tabContainer}>
        {renderTabButton('regular', 'Regular', availabilities.filter(a => a.type === AvailabilityType.REGULAR).length)}
        {renderTabButton('overrides', 'Overrides', availabilities.filter(a => a.type === AvailabilityType.OVERRIDE || a.type === AvailabilityType.SPECIAL).length)}
        {renderTabButton('pending', 'Pending', availabilities.filter(a => a.status === AvailabilityStatus.PENDING).length)}
      </View>

      {/* Content */}
      <ScrollView
        style={styles.content}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            colors={[Colors.primary]}
            tintColor={Colors.primary}
          />
        }
      >
        {availabilities.length > 0 ? (
          <View style={styles.availabilityList}>
            {availabilities.map(renderAvailabilityCard)}
          </View>
        ) : (
          <View style={styles.emptyState}>
            <Icon name="schedule" size={48} color={Colors.gray400} />
            <DarkTitle level={4} style={styles.emptyTitle}>
              No Availability Found
            </DarkTitle>
            <DarkBody style={styles.emptyMessage}>
              {selectedTab === 'regular' && 'No regular schedule has been set up yet.'}
              {selectedTab === 'overrides' && 'No schedule overrides have been created.'}
              {selectedTab === 'pending' && 'No availability changes are pending approval.'}
            </DarkBody>
            <Button
              title="Add Availability"
              onPress={handleAddAvailability}
              style={styles.emptyActionButton}
            />
            <Button
              title="Block June 20, 2025 (11:00-11:45)"
              onPress={blockSpecificDateTime}
              style={{
                ...styles.emptyActionButton,
                backgroundColor: Colors.error,
                marginTop: 10,
              }}
            />
          </View>
        )}
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.02,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  headerTitle: {
    flex: 1,
    textAlign: 'center',
    marginHorizontal: width * 0.05,
  },
  staffInfo: {
    padding: width * 0.05,
    backgroundColor: Colors.white,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  staffSubtitle: {
    color: Colors.gray600,
    marginTop: height * 0.005,
  },
  salonHoursReference: {
    marginTop: height * 0.015,
    padding: width * 0.03,
    backgroundColor: Colors.gray50,
    borderRadius: width * 0.02,
    borderWidth: 1,
    borderColor: Colors.gray200,
  },
  salonHoursHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: height * 0.005,
  },
  salonHoursTitle: {
    color: Colors.gray700,
    fontWeight: '600',
  },
  salonHoursText: {
    color: Colors.gray600,
    lineHeight: 18,
  },
  initializeButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.primary + '20',
    paddingHorizontal: width * 0.025,
    paddingVertical: height * 0.006,
    borderRadius: width * 0.015,
  },
  initializeButtonText: {
    color: Colors.primary,
    marginLeft: width * 0.01,
    fontWeight: '600',
  },
  loadingContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
  },
  tabContainer: {
    flexDirection: 'row',
    backgroundColor: Colors.white,
    paddingHorizontal: width * 0.05,
    paddingVertical: height * 0.01,
    borderBottomWidth: 1,
    borderBottomColor: Colors.gray200,
  },
  tabButton: {
    flex: 1,
    paddingVertical: height * 0.015,
    paddingHorizontal: width * 0.03,
    borderRadius: width * 0.02,
    marginHorizontal: width * 0.01,
    alignItems: 'center',
  },
  activeTabButton: {
    backgroundColor: Colors.primary + '20',
  },
  tabButtonText: {
    color: Colors.gray600,
    fontWeight: '500',
  },
  activeTabButtonText: {
    color: Colors.primary,
    fontWeight: '600',
  },
  content: {
    flex: 1,
  },
  availabilityList: {
    padding: width * 0.05,
    gap: height * 0.015,
  },
  availabilityCard: {
    backgroundColor: Colors.white,
    borderRadius: width * 0.03,
    padding: width * 0.04,
    shadowColor: Colors.black,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    marginBottom: height * 0.015,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: height * 0.015,
  },
  cardHeaderLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  typeBadge: {
    paddingHorizontal: width * 0.02,
    paddingVertical: height * 0.005,
    borderRadius: width * 0.015,
    marginRight: width * 0.02,
  },
  typeBadgeText: {
    fontSize: 11,
    fontWeight: '600',
  },
  statusBadge: {
    paddingHorizontal: width * 0.02,
    paddingVertical: height * 0.005,
    borderRadius: width * 0.015,
  },
  statusBadgeText: {
    fontSize: 11,
    fontWeight: '600',
  },
  cardActions: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  actionButton: {
    padding: width * 0.02,
    marginLeft: width * 0.01,
  },
  cardContent: {
    gap: height * 0.012,
  },
  dayAndHours: {
    gap: height * 0.008,
  },
  workingHoursContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
  },
  timeDisplay: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  workingHours: {
    marginLeft: width * 0.02,
    color: Colors.textPrimary,
    fontWeight: '600',
  },
  timelinePreview: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingTop: height * 0.01,
    borderTopWidth: 1,
    borderTopColor: Colors.gray200,
  },
  timelineLabel: {
    color: Colors.gray500,
    fontStyle: 'italic',
  },
  salonIndicator: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.success + '10',
    paddingHorizontal: width * 0.02,
    paddingVertical: height * 0.005,
    borderRadius: width * 0.015,
    alignSelf: 'flex-start',
  },
  salonIndicatorText: {
    color: Colors.success,
    marginLeft: width * 0.01,
    fontWeight: '500',
    fontSize: 11,
  },
  cardTitle: {
    marginBottom: height * 0.005,
  },
  timeSlots: {
    color: Colors.gray700,
    lineHeight: 20,
  },
  cardFooter: {
    marginTop: height * 0.01,
    gap: height * 0.005,
  },
  totalHours: {
    color: Colors.primary,
    fontWeight: '600',
  },
  notes: {
    color: Colors.gray600,
    fontStyle: 'italic',
  },
  noteContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  noteIcon: {
    marginRight: width * 0.01,
  },
  salonGeneratedNote: {
    color: Colors.success,
    fontStyle: 'normal',
    fontWeight: '500',
  },
  emptyState: {
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: height * 0.08,
    paddingHorizontal: width * 0.1,
  },
  emptyTitle: {
    marginTop: height * 0.02,
    marginBottom: height * 0.01,
    textAlign: 'center',
    color: Colors.gray600,
  },
  emptyMessage: {
    textAlign: 'center',
    color: Colors.gray500,
    lineHeight: 20,
    marginBottom: height * 0.03,
  },
  emptyActionButton: {
    minWidth: width * 0.4,
  },
});
