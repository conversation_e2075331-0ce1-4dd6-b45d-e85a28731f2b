import { MigrationInterface, QueryRunner } from 'typeorm';

export class FixStaffPermissionConstraint1748600000000 implements MigrationInterface {
  name = 'FixStaffPermissionConstraint1748600000000';

  public async up(queryRunner: QueryRunner): Promise<void> {
    // Drop the existing unique constraint that causes issues with updates
    await queryRunner.query(`
      ALTER TABLE "staff_permissions" 
      DROP CONSTRAINT IF EXISTS "UQ_dd622694400762dc0f8def366df"
    `);

    // Create a partial unique index that only applies to active permissions
    // This allows multiple inactive records with the same staffSalonAccessId and permissionId
    await queryRunner.query(`
      CREATE UNIQUE INDEX "IDX_staff_permission_active_unique" 
      ON "staff_permissions" ("staffSalonAccessId", "permissionId") 
      WHERE "isActive" = true
    `);

    console.log('✅ Migration: Fixed staff permission constraint to allow proper updates');
  }

  public async down(queryRunner: QueryRunner): Promise<void> {
    // Drop the partial unique index
    await queryRunner.query(`
      DROP INDEX IF EXISTS "IDX_staff_permission_active_unique"
    `);

    // Recreate the original unique constraint
    await queryRunner.query(`
      ALTER TABLE "staff_permissions" 
      ADD CONSTRAINT "UQ_dd622694400762dc0f8def366df" 
      UNIQUE ("staffSalonAccessId", "permissionId")
    `);

    console.log('✅ Migration: Reverted staff permission constraint changes');
  }
}
